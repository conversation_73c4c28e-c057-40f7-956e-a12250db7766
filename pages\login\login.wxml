<!--login.wxml-->
<view class="login-container" style="height: 1399rpx; display: flex; box-sizing: border-box">
  <!-- 顶部Logo区域 -->
  <view class="logo-section">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">包工头零工平台</text>
    <text class="app-slogan">连接雇主与工人，让工作更简单</text>
  </view>

  <!-- 登录表单区域 -->
  <view class="login-form">
    <!-- 微信登录按钮 -->
    <button
      class="wechat-login-btn"
      bindtap="onWechatLogin"
      wx:if="{{!isLoggedIn}}"
    >
      <image class="wechat-icon" src="/images/wechat-icon.png"></image>
      <text>微信一键登录</text>
    </button>

    <!-- 登录成功状态 -->
    <view class="login-success" wx:if="{{isLoggedIn}}">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <text class="welcome-text">欢迎回来，{{userInfo.nickName}}</text>
      <button class="enter-app-btn" bindtap="enterApp">进入应用</button>
    </view>

    <!-- 用户类型选择 -->
    <view class="user-type-section" wx:if="{{showUserTypeSelection}}">
      <text class="section-title">请选择您的身份</text>
      <view class="type-options">
        <view 
          class="type-option {{selectedUserType === 'worker' ? 'selected' : ''}}"
          data-type="worker"
          bindtap="selectUserType"
        >
          <image class="type-icon" src="/images/worker-icon.png"></image>
          <text class="type-name">我是工人</text>
          <text class="type-desc">寻找工作机会</text>
        </view>
        <view 
          class="type-option {{selectedUserType === 'merchant' ? 'selected' : ''}}"
          data-type="merchant"
          bindtap="selectUserType"
        >
          <image class="type-icon" src="/images/merchant-icon.png"></image>
          <text class="type-name">我是雇主</text>
          <text class="type-desc">发布工作任务</text>
        </view>
        <view 
          class="type-option {{selectedUserType === 'both' ? 'selected' : ''}}"
          data-type="both"
          bindtap="selectUserType"
        >
          <image class="type-icon" src="/images/both-icon.png"></image>
          <text class="type-name">两者都是</text>
          <text class="type-desc">既找工作也发任务</text>
        </view>
      </view>
      <button 
        class="confirm-type-btn {{selectedUserType ? 'active' : ''}}"
        bindtap="confirmUserType"
        disabled="{{!selectedUserType}}"
      >
        确认身份
      </button>
    </view>
  </view>

  <!-- 底部协议区域 -->
  <view class="agreement-section">
    <text class="agreement-text">
      登录即表示同意
      <text class="link" bindtap="showUserAgreement">《用户协议》</text>
      和
      <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
    </text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>
