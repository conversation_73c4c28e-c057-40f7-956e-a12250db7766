/**
 * API请求工具
 * 统一处理HTTP请求、身份验证、错误处理
 */

// API基础配置
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:8080',
  timeout: 10000,
  version: 'v1'
};

/**
 * 获取完整的API URL
 * @param {string} path - API路径
 * @returns {string} 完整的URL
 */
function getApiUrl(path) {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${API_CONFIG.baseURL}/api/${API_CONFIG.version}${cleanPath}`;
}

/**
 * 获取请求头
 * @param {boolean} needAuth - 是否需要身份验证
 * @returns {Object} 请求头对象
 */
function getHeaders(needAuth = true) {
  const headers = {
    'Content-Type': 'application/json'
  };

  if (needAuth) {
    const token = wx.getStorageSync('access_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return headers;
}

/**
 * 处理API响应
 * @param {Object} response - 微信请求响应
 * @returns {Promise} 处理后的响应
 */
function handleResponse(response) {
  return new Promise((resolve, reject) => {
    const { statusCode, data } = response;

    console.log('API Response:', { statusCode, data });

    // 成功响应
    if (statusCode >= 200 && statusCode < 300) {
      resolve(data);
      return;
    }

    // 401 未授权 - 清除登录信息并跳转到登录页
    if (statusCode === 401) {
      wx.removeStorageSync('access_token');
      wx.removeStorageSync('userInfo');
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/login/login'
        });
      }, 1500);
      reject(new Error('登录已过期'));
      return;
    }

    // 其他错误
    let errorMessage = `请求失败 (${statusCode})`;

    if (data) {
      if (typeof data === 'string') {
        errorMessage = data;
      } else if (data.detail) {
        errorMessage = typeof data.detail === 'string' ? data.detail : JSON.stringify(data.detail);
      } else if (data.message) {
        errorMessage = typeof data.message === 'string' ? data.message : JSON.stringify(data.message);
      } else if (data.error) {
        errorMessage = typeof data.error === 'string' ? data.error : JSON.stringify(data.error);
      }
    }

    reject(new Error(errorMessage));
  });
}

/**
 * 统一的HTTP请求方法
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求结果
 */
function request(options) {
  const {
    url,
    method = 'GET',
    data = {},
    needAuth = true,
    showLoading = false,
    loadingText = '请求中...'
  } = options;

  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    console.log('API Request:', {
      url: getApiUrl(url),
      method,
      data,
      headers: getHeaders(needAuth)
    });

    wx.request({
      url: getApiUrl(url),
      method,
      data,
      header: getHeaders(needAuth),
      timeout: API_CONFIG.timeout,
      success: (response) => {
        if (showLoading) {
          wx.hideLoading();
        }
        handleResponse(response)
          .then(resolve)
          .catch(reject);
      },
      fail: (error) => {
        if (showLoading) {
          wx.hideLoading();
        }
        console.error('Request failed:', error);
        
        let errorMessage = '网络请求失败';
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请检查网络设置';
          }
        }
        
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
        
        reject(new Error(errorMessage));
      }
    });
  });
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 查询参数
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
function get(url, params = {}, options = {}) {
  // 将参数添加到URL中
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const fullUrl = queryString ? `${url}?${queryString}` : url;
  
  return request({
    url: fullUrl,
    method: 'GET',
    ...options
  });
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  });
}

// 导出API工具
module.exports = {
  // 基础请求方法
  request,
  get,
  post,
  put,
  delete: del,
  
  // 配置
  config: API_CONFIG,
  
  // 工具方法
  getApiUrl,
  getHeaders
};
