/* pages/tasks/detail/detail.wxss */
.detail-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 40rpx;
  padding-bottom: 120rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 任务头部 */
.task-header {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.task-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.task-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  margin-right: 20rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  min-width: 100rpx;
  text-align: center;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.status-text.status-pending {
  color: #1890ff;
  background: #e6f7ff;
}

.status-text.status-accepted {
  color: #fa8c16;
  background: #fff2e8;
}

.status-text.status-in_progress {
  color: #52c41a;
  background: #f6ffed;
}

.status-text.status-completed {
  color: #722ed1;
  background: #f9f0ff;
}

.status-text.status-cancelled {
  color: #ff4d4f;
  background: #fff1f0;
}

.task-reward-section {
  text-align: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 16rpx;
}

.reward-label {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.reward-amount {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
}

/* 信息区域 */
.info-section,
.description-section,
.location-section,
.publisher-section,
.order-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.info-item,
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

.info-item:last-child,
.order-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.info-label,
.order-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 1.4;
}

.info-value,
.order-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  margin-left: 20rpx;
  max-width: 400rpx;
}

.order-value.order-pending {
  color: #1890ff;
}

.order-value.order-accepted {
  color: #fa8c16;
}

.order-value.order-in_progress {
  color: #52c41a;
}

.order-value.order-completed {
  color: #722ed1;
}

/* 描述区域 */
.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  background: #f8f9fa;
  padding: 30rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  min-height: 80rpx;
}

/* 位置区域 */
.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 12rpx;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

.location-icon {
  font-size: 28rpx;
  color: #667eea;
}

.location-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.distance-text {
  font-size: 24rpx;
  color: #666;
  background: #e6f7ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.map-container {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-map {
  width: 100%;
  height: 300rpx;
}

.map-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.map-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.map-btn.primary {
  background: #667eea;
  color: white;
}

.map-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

/* 发布者信息 */
.publisher-info {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  background: #f8f9fa;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.publisher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 4rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.publisher-stats {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.credit-score,
.verification-level {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.credit-score {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.verification-level {
  background: #f0f3ff;
  color: #667eea;
  border: 1rpx solid #adc6ff;
}

/* 联系区域 */
.contact-section {
  text-align: center;
}

.contact-btn {
  width: 200rpx;
  height: 70rpx;
  background: #667eea;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  border: none;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 订单信息 */
.order-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  border: 2rpx solid #e6f7ff;
}

/* 底部占位 */
.bottom-placeholder {
  height: 40rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 40rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200rpx;
  white-space: nowrap;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.action-btn[disabled] {
  background: #ccc;
  color: #999;
  box-shadow: none;
  border: none;
}

.action-btn:active:not([disabled]) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}