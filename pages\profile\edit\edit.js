// pages/profile/edit/edit.js
const authApi = require('../../../utils/auth-api.js');

Page({
  data: {
    formData: {
      nickname: '',
      avatar_url: '',
      user_type: '',
      phone: '',
      phone_verified: false,
      real_name: '',
      id_card: '',
      address: ''
    },
    userTypes: [
      { value: 'worker', label: '工人' },
      { value: 'merchant', label: '雇主' },
      { value: 'both', label: '工人&雇主' }
    ],
    userTypeIndex: 0,
    showVerifyModal: false,
    verifyCode: '',
    canSendCode: true,
    sendCodeText: '发送验证码',
    countdown: 0,
    isLoading: false
  },

  onLoad(options) {
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      if (!authApi.isLoggedIn()) {
        authApi.redirectToLogin();
        return;
      }

      const userInfo = authApi.getLocalUserInfo();
      if (userInfo) {
        // 找到用户类型对应的索引
        const typeIndex = this.data.userTypes.findIndex(type => type.value === userInfo.user_type);
        
        this.setData({
          formData: {
            nickname: userInfo.nickname || '',
            avatar_url: userInfo.avatar_url || '',
            user_type: userInfo.user_type || 'worker',
            phone: userInfo.phone || '',
            phone_verified: !!userInfo.phone,
            real_name: userInfo.real_name || '',
            id_card: userInfo.id_card || '',
            address: userInfo.address || ''
          },
          userTypeIndex: typeIndex >= 0 ? typeIndex : 0
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      });
    }
  },

  /**
   * 选择头像
   */
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        // 更新头像显示
        this.setData({
          'formData.avatar_url': tempFilePath
        });

        // TODO: 上传头像到服务器
        wx.showToast({
          title: '头像上传功能开发中',
          icon: 'none'
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
      }
    });
  },

  /**
   * 输入框变化
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 用户类型变化
   */
  onUserTypeChange(e) {
    const index = parseInt(e.detail.value);
    const userType = this.data.userTypes[index];
    
    this.setData({
      userTypeIndex: index,
      'formData.user_type': userType.value
    });
  },

  /**
   * 验证手机号
   */
  verifyPhone() {
    const phone = this.data.formData.phone;
    
    if (!phone) {
      wx.showToast({
        title: '请先输入手机号',
        icon: 'none'
      });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showVerifyModal: true,
      verifyCode: ''
    });
  },

  /**
   * 关闭验证弹窗
   */
  closeVerifyModal() {
    this.setData({
      showVerifyModal: false,
      verifyCode: ''
    });
  },

  /**
   * 验证码输入
   */
  onCodeInput(e) {
    this.setData({
      verifyCode: e.detail.value
    });
  },

  /**
   * 发送验证码
   */
  async sendVerifyCode() {
    if (!this.data.canSendCode) {
      return;
    }

    try {
      await authApi.sendSmsCode(this.data.formData.phone);
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      });

      // 开始倒计时
      this.startCountdown();

    } catch (error) {
      console.error('发送验证码失败:', error);
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始倒计时
   */
  startCountdown() {
    let countdown = 60;
    this.setData({
      canSendCode: false,
      countdown: countdown,
      sendCodeText: `${countdown}s后重发`
    });

    const timer = setInterval(() => {
      countdown--;
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canSendCode: true,
          sendCodeText: '重新发送'
        });
      } else {
        this.setData({
          countdown: countdown,
          sendCodeText: `${countdown}s后重发`
        });
      }
    }, 1000);
  },

  /**
   * 确认验证码
   */
  async confirmVerifyCode() {
    try {
      const verifyData = {
        phone: this.data.formData.phone,
        code: this.data.verifyCode
      };

      await authApi.verifyPhone(verifyData);

      wx.showToast({
        title: '验证成功',
        icon: 'success'
      });

      this.setData({
        'formData.phone_verified': true,
        showVerifyModal: false
      });

    } catch (error) {
      console.error('验证失败:', error);
      wx.showToast({
        title: error.message || '验证失败',
        icon: 'none'
      });
    }
  },

  /**
   * 保存用户信息
   */
  async saveProfile() {
    if (this.data.isLoading) {
      return;
    }

    // 验证必填字段
    if (!this.data.formData.nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true
    });

    try {
      const updateData = {
        nickname: this.data.formData.nickname.trim(),
        user_type: this.data.formData.user_type,
        phone: this.data.formData.phone || null,
        real_name: this.data.formData.real_name || null,
        id_card: this.data.formData.id_card || null,
        address: this.data.formData.address || null
      };

      // 如果有头像变化，也需要上传
      if (this.data.formData.avatar_url && !this.data.formData.avatar_url.startsWith('http')) {
        // TODO: 上传头像
        updateData.avatar_url = this.data.formData.avatar_url;
      }

      const updatedUserInfo = await authApi.updateUserProfile(updateData);
      
      // 更新本地存储
      wx.setStorageSync('userInfo', updatedUserInfo);

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  /**
   * 取消编辑
   */
  cancel() {
    wx.navigateBack();
  }
});
