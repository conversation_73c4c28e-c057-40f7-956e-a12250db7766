// pages/orders/orders.js
const api = require('../../utils/api.js');
const authApi = require('../../utils/auth-api.js');

Page({
  data: {
    currentTab: 0,
    statusTabs: [
      { name: '全部', status: 'all', count: 0 },
      { name: '待接单', status: 'pending', count: 0 },
      { name: '进行中', status: 'in_progress', count: 0 },
      { name: '已完成', status: 'completed', count: 0 },
      { name: '已取消', status: 'cancelled', count: 0 }
    ],
    orderList: [],
    loading: false,
    refreshing: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.checkLoginAndLoad();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!authApi.isLoggedIn()) {
      authApi.redirectToLogin();
      return;
    }
    this.loadOrders();
    this.loadOrderCounts();
  },

  onShow() {
    // 页面显示时刷新数据
    this.setData({
      page: 1
    });
    this.loadOrders();
    this.loadOrderCounts();
  },

  // 切换状态标签
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index,
      page: 1,
      orderList: [],
      hasMore: true
    });
    this.loadOrders();
  },

  // 加载订单列表
  async loadOrders() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const currentStatus = this.data.statusTabs[this.data.currentTab].status;
      const params = {
        page: this.data.page,
        size: 10
      };

      // 如果不是全部状态，添加状态过滤
      if (currentStatus !== 'all') {
        params.status = currentStatus;
      }

      let response;
      let newOrders = [];

      try {
        response = await api.get('/orders', params);
        console.log('订单列表API响应:', response);

        // 根据API文档，数据在orders字段中
        const ordersData = response.orders || response.data || response || [];
        console.log('解析的订单数据:', ordersData);
        console.log('当前用户ID:', authApi.getLocalUserInfo()?.id);

        if (Array.isArray(ordersData)) {
          const currentUserId = authApi.getLocalUserInfo()?.id;
          newOrders = ordersData.map(order => {
            const isPublisher = order.merchant_id === currentUserId;
            const isWorker = order.worker_id === currentUserId;

            return {
              ...order,
              // 字段映射
              order_number: order.id ? `ORD${order.id.toString().padStart(6, '0')}` : '',
              amount: order.task_reward,
              address: order.task_address,
              estimated_hours: order.estimated_hours || 1,
              task_category: this.getCategoryLabel(order.task_category),
              // 状态和时间
              status_text: this.getStatusText(order.status),
              created_at: this.formatTime(order.created_at),
              // 添加用户身份标识
              isPublisher,
              isWorker,
              // 添加按钮显示逻辑
              showCancelButton: (isPublisher || isWorker) && (order.status === 'pending' || order.status === 'accepted' || order.status === 'in_progress'),
              showContactButton: isPublisher && (order.status === 'accepted' || order.status === 'in_progress' || order.status === 'worker_completed'),
              // 工人状态流转按钮
              showStartWorkButton: isWorker && order.status === 'accepted',
              showCompleteWorkButton: isWorker && order.status === 'in_progress',
              // 发布者确认按钮
              showConfirmCompleteButton: isPublisher && order.status === 'worker_completed',
              showRateButton: isPublisher && order.status === 'completed' && !order.worker_rating,
              showDeleteButton: (isPublisher || isWorker) && (order.status === 'completed' || order.status === 'cancelled')
            };
          });
        }
      } catch (apiError) {
        console.error('获取订单列表失败:', apiError);
        wx.showToast({
          title: '获取订单失败',
          icon: 'none'
        });
        return; // 如果API调用失败，直接返回，不更新数据
      }

      // 只有成功获取数据时才更新
      console.log('处理后的订单数据:', newOrders);
      console.log('当前页码:', this.data.page);

      this.setData({
        orderList: this.data.page === 1 ? newOrders : [...this.data.orderList, ...newOrders],
        hasMore: response && (response.has_more || response.hasMore || (newOrders.length === 10)),
        page: this.data.page + 1
      });

      console.log('更新后的订单列表:', this.data.orderList);

    } catch (error) {
      console.error('加载订单失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  // 加载订单数量统计
  async loadOrderCounts() {
    try {
      // 使用新的订单统计接口
      const response = await api.get('/orders/stats');
      console.log('订单统计API响应:', response);

      // 根据API文档，响应格式为 { total, pending, accepted, in_progress, worker_completed, completed, cancelled }
      const stats = response.data || response;
      const counts = {
        all: stats.total || 0,
        pending: stats.pending || 0,
        accepted: stats.accepted || 0,
        in_progress: (stats.in_progress || 0) + (stats.worker_completed || 0), // 合并进行中状态
        completed: stats.completed || 0,
        cancelled: stats.cancelled || 0
      };

      const statusTabs = this.data.statusTabs.map(tab => ({
        ...tab,
        count: counts[tab.status] || 0
      }));

      this.setData({ statusTabs });
    } catch (error) {
      console.error('加载订单统计失败:', error);
      // 统计失败不影响主要功能，静默处理
    }
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      orderList: [],
      hasMore: true
    });
    this.loadOrders();
    this.loadOrderCounts();
  },

  // 加载更多
  loadMore() {
    this.loadOrders();
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/orders/detail/detail?id=${orderId}`
    });
  },

  // 取消订单
  async cancelOrder(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const orderId = e.currentTarget.dataset.id;

    const result = await wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      confirmText: '确认',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      // 先尝试专用的取消订单接口
      let response;
      try {
        response = await api.post(`/orders/${orderId}/cancel`);
        console.log('取消订单响应:', response);
      } catch (cancelError) {
        console.log('专用取消接口失败，尝试通用状态更新接口:', cancelError);
        // 如果专用接口失败，尝试使用通用状态更新接口
        response = await api.post('/orders/update-status', {
          order_id: orderId,
          status: 'cancelled'
        });
        console.log('通用状态更新响应:', response);
      }

      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });
      this.onRefresh();
    } catch (error) {
      console.error('取消订单失败:', error);
      console.error('错误详情:', error.message);

      // 显示具体的错误信息
      const errorMessage = error.message || '取消失败';
      wx.showToast({
        title: errorMessage.length > 20 ? '取消失败，请重试' : errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 联系接单者
  contactWorker(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/chat/chat?orderId=${orderId}`
    });
  },

  // 开始工作
  async startWork(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;

    const result = await wx.showModal({
      title: '开始工作',
      content: '确定要开始执行这个任务吗？',
      confirmText: '开始',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: orderId,
        status: 'in_progress'
      });
      console.log('开始工作响应:', response);

      wx.showToast({
        title: '已开始工作',
        icon: 'success'
      });
      this.onRefresh();
    } catch (error) {
      console.error('开始工作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 完成工作
  async completeWork(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;

    const result = await wx.showModal({
      title: '完成工作',
      content: '确定已完成工作，等待发布者确认吗？',
      confirmText: '完成',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: orderId,
        status: 'worker_completed'
      });
      console.log('完成工作响应:', response);

      wx.showToast({
        title: '工作已完成，等待确认',
        icon: 'success'
      });
      this.onRefresh();
    } catch (error) {
      console.error('完成工作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 确认完成
  async confirmComplete(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.id;

    const result = await wx.showModal({
      title: '确认完成',
      content: '确认工人已完成工作，订单将标记为已完成？',
      confirmText: '确认',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: orderId,
        status: 'completed'
      });
      console.log('确认完成响应:', response);

      wx.showToast({
        title: '订单已完成',
        icon: 'success'
      });
      this.onRefresh();
    } catch (error) {
      console.error('确认完成失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 评价订单
  rateOrder(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/orders/rate/rate?orderId=${orderId}`
    });
  },

  // 删除订单
  async deleteOrder(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const orderId = e.currentTarget.dataset.id;

    const result = await wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      confirmText: '删除',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.delete(`/orders/${orderId}`);
      console.log('删除订单响应:', response);

      wx.showToast({
        title: '订单已删除',
        icon: 'success'
      });
      this.onRefresh();
    } catch (error) {
      console.error('删除订单失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  // 跳转到任务大厅
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'worker_completed': '工人已完成',
      'completed': '已完成',
      'cancelled': '已取消'
    };

    // 添加调试信息
    if (!statusMap[status]) {
      console.log('未知状态:', status);
    }

    return statusMap[status] || status;
  },

  // 获取分类标签
  getCategoryLabel(categoryValue) {
    const categories = {
      'cleaning': '清洁',
      'moving': '搬运',
      'delivery': '配送',
      'repair': '维修',
      'installation': '安装',
      'other': '其他'
    };
    return categories[categoryValue] || categoryValue || '其他';
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 24小时内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  }
});
