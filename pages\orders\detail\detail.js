// pages/orders/detail/detail.js
const api = require('../../../utils/api.js');
const authApi = require('../../../utils/auth-api.js');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    taskDetail: null,
    mapMarkers: [],
    isLoading: false,
    showActionBar: false,
    canCancelOrder: false,
    canContactWorker: false,
    canCompleteOrder: false,
    canRateOrder: false,
    canDeleteOrder: false,
    currentUserId: null,
    statusText: '',
    createdAtText: '',
    acceptedAtText: '',
    completedAtText: '',
    orderTypeText: ''
  },

  onLoad(options) {
    const orderId = options.id;

    if (!orderId) {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      orderId: parseInt(orderId)
    });

    this.checkLoginAndLoad();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!authApi.isLoggedIn()) {
      authApi.redirectToLogin();
      return;
    }

    const userInfo = authApi.getLocalUserInfo();
    this.setData({
      currentUserId: userInfo ? userInfo.id : null
    });

    this.loadOrderDetail();
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    if (this.data.isLoading) return;

    this.setData({
      isLoading: true
    });

    try {
      let orderDetail;

      try {
        // 从后台获取订单详情
        const response = await api.get(`/orders/${this.data.orderId}`);
        console.log('订单详情API响应:', response);

        // 更灵活的数据提取
        orderDetail = response.data || response.order || response;
        console.log('提取的订单详情:', orderDetail);

        // 如果有task_id，尝试获取任务详情来补充准确的时长信息
        if (orderDetail.task_id) {
          try {
            const taskResponse = await api.get(`/tasks/${orderDetail.task_id}`);
            const taskDetail = taskResponse.data || taskResponse.task || taskResponse;
            console.log('任务详情API响应:', taskDetail);

            // 用任务的时长覆盖订单的时长（如果任务时长存在且不同）
            if (taskDetail.estimated_hours && taskDetail.estimated_hours !== orderDetail.estimated_hours) {
              orderDetail.estimated_hours = taskDetail.estimated_hours;
              console.log('使用任务的准确时长:', taskDetail.estimated_hours);
            }
          } catch (taskError) {
            console.log('获取任务详情失败，使用订单中的时长:', taskError);
          }
        }

        // 如果是数组，取第一个元素
        if (Array.isArray(orderDetail) && orderDetail.length > 0) {
          orderDetail = orderDetail[0];
        }

      } catch (apiError) {
        console.error('获取订单详情失败:', apiError);
        wx.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
        return;
      }

      if (!orderDetail || (typeof orderDetail === 'object' && Object.keys(orderDetail).length === 0)) {
        wx.showToast({
          title: '订单不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 设置地图标记
      const mapMarkers = [];
      if (orderDetail.task_latitude && orderDetail.task_longitude) {
        mapMarkers.push({
          id: 1,
          latitude: orderDetail.task_latitude,
          longitude: orderDetail.task_longitude,
          title: orderDetail.task_title
        });
      }

      // 处理订单数据，添加字段映射
      console.log('原始订单数据字段:', Object.keys(orderDetail));
      console.log('estimated_hours值:', orderDetail.estimated_hours);
      console.log('task_duration值:', orderDetail.task_duration);
      console.log('duration值:', orderDetail.duration);

      const processedOrder = {
        ...orderDetail,
        order_number: orderDetail.id ? `ORD${orderDetail.id.toString().padStart(6, '0')}` : '',
        amount: orderDetail.task_reward || orderDetail.amount || 0,
        address: orderDetail.task_address,
        task_category: this.getCategoryLabel(orderDetail.task_category),
        estimated_hours: orderDetail.estimated_hours || orderDetail.task_duration || orderDetail.duration || 1,
        publisher_nickname: orderDetail.merchant_nickname || orderDetail.publisher_nickname || '未知用户',
        publisher_phone: orderDetail.merchant_phone || orderDetail.publisher_phone || '',
        worker_nickname: orderDetail.worker_nickname || orderDetail.worker_name || '',
        worker_phone: orderDetail.worker_phone || ''
      };

      console.log('处理后的订单数据:', processedOrder);

      this.setData({
        orderDetail: processedOrder,
        mapMarkers: mapMarkers,
        statusText: this.getStatusText(orderDetail.status),
        createdAtText: this.formatDate(orderDetail.created_at),
        acceptedAtText: this.formatDate(orderDetail.accepted_at),
        completedAtText: this.formatDate(orderDetail.completed_at),
        orderTypeText: this.getOrderTypeText(orderDetail)
      });

      console.log('设置后的页面数据:', this.data.orderDetail);

      // 更新操作按钮状态
      this.updateActionButtons();

    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  /**
   * 更新操作按钮状态
   */
  updateActionButtons() {
    const { orderDetail, currentUserId } = this.data;
    if (!orderDetail || !currentUserId) return;

    const isPublisher = orderDetail.merchant_id === currentUserId;
    const isWorker = orderDetail.worker_id === currentUserId;
    const status = orderDetail.status;

    this.setData({
      canCancelOrder: (isPublisher || isWorker) && (status === 'pending' || status === 'accepted' || status === 'in_progress'),
      canContactWorker: isPublisher && (status === 'accepted' || status === 'in_progress'),
      // 工人状态流转按钮
      canStartWork: isWorker && status === 'accepted', // 接单→进行中
      canCompleteWork: isWorker && status === 'in_progress', // 进行中→完成（工人完成）
      // 发布者确认完成
      canConfirmComplete: isPublisher && status === 'worker_completed', // 确认工人完成
      canRateOrder: isPublisher && status === 'completed' && !orderDetail.worker_rating,
      canDeleteOrder: (isPublisher || isWorker) && (status === 'completed' || status === 'cancelled'),
      showActionBar: true
    });
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'worker_completed': '工人已完成',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  },

  /**
   * 获取订单类型文本
   */
  getOrderTypeText(orderDetail) {
    if (!orderDetail) return '未知';

    const currentUserId = this.data.currentUserId;
    const isPublisher = orderDetail.merchant_id === currentUserId;
    const isWorker = orderDetail.worker_id === currentUserId;

    if (isPublisher) {
      return '我发布的';
    } else if (isWorker) {
      return '我接受的';
    } else {
      return '未知';
    }
  },

  /**
   * 获取分类标签
   */
  getCategoryLabel(categoryValue) {
    const categories = {
      'cleaning': '清洁',
      'moving': '搬运',
      'delivery': '配送',
      'repair': '维修',
      'installation': '安装',
      'other': '其他'
    };
    return categories[categoryValue] || categoryValue || '其他';
  },

  /**
   * 格式化日期
   */
  formatDate(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 取消订单
   */
  async cancelOrder() {
    const result = await wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      confirmText: '确认',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      // 先尝试专用的取消订单接口
      let response;
      try {
        response = await api.post(`/orders/${this.data.orderId}/cancel`);
        console.log('取消订单响应:', response);
      } catch (cancelError) {
        console.log('专用取消接口失败，尝试通用状态更新接口:', cancelError);
        // 如果专用接口失败，尝试使用通用状态更新接口
        response = await api.post('/orders/update-status', {
          order_id: this.data.orderId,
          status: 'cancelled'
        });
        console.log('通用状态更新响应:', response);
      }

      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });

      // 刷新订单详情
      this.loadOrderDetail();
    } catch (error) {
      console.error('取消订单失败:', error);
      console.error('错误详情:', error.message);

      // 显示具体的错误信息
      const errorMessage = error.message || '取消失败';
      wx.showToast({
        title: errorMessage.length > 20 ? '取消失败，请重试' : errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 联系对方
   */
  contactUser() {
    const orderId = this.data.orderId;
    wx.navigateTo({
      url: `/pages/chat/chat?orderId=${orderId}`
    });
  },

  /**
   * 开始工作（接单→进行中）
   */
  async startWork() {
    const result = await wx.showModal({
      title: '开始工作',
      content: '确定要开始执行这个任务吗？',
      confirmText: '开始',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: this.data.orderId,
        status: 'in_progress'
      });
      console.log('开始工作响应:', response);

      wx.showToast({
        title: '已开始工作',
        icon: 'success'
      });

      this.loadOrderDetail();
    } catch (error) {
      console.error('开始工作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 完成工作（进行中→工人完成）
   */
  async completeWork() {
    const result = await wx.showModal({
      title: '完成工作',
      content: '确定已完成工作，等待发布者确认吗？',
      confirmText: '完成',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: this.data.orderId,
        status: 'worker_completed'
      });
      console.log('完成工作响应:', response);

      wx.showToast({
        title: '工作已完成，等待确认',
        icon: 'success'
      });

      this.loadOrderDetail();
    } catch (error) {
      console.error('完成工作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 确认完成（发布者确认）
   */
  async confirmComplete() {
    const result = await wx.showModal({
      title: '确认完成',
      content: '确认工人已完成工作，订单将标记为已完成？',
      confirmText: '确认',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.post('/orders/update-status', {
        order_id: this.data.orderId,
        status: 'completed'
      });
      console.log('确认完成响应:', response);

      wx.showToast({
        title: '订单已完成',
        icon: 'success'
      });

      this.loadOrderDetail();
    } catch (error) {
      console.error('确认完成失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 评价订单
   */
  rateOrder() {
    const orderId = this.data.orderId;
    wx.navigateTo({
      url: `/pages/orders/rate/rate?orderId=${orderId}`
    });
  },

  /**
   * 删除订单
   */
  async deleteOrder() {
    const result = await wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      confirmText: '删除',
      cancelText: '取消'
    });

    if (!result.confirm) return;

    try {
      const response = await api.delete(`/orders/${this.data.orderId}`);
      console.log('删除订单响应:', response);

      wx.showToast({
        title: '订单已删除',
        icon: 'success'
      });
      
      // 返回上一页
      wx.navigateBack();
    } catch (error) {
      console.error('删除订单失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看任务详情
   */
  goToTaskDetail() {
    const taskId = this.data.orderDetail?.task_id;
    if (taskId) {
      wx.navigateTo({
        url: `/pages/tasks/detail/detail?id=${taskId}`
      });
    }
  }
});
