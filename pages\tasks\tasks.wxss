/* tasks.wxss */
.tasks-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: white;
  gap: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 80rpx 0 20rpx;
  background: #f8f9fa;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
}

.search-input:focus {
  border-color: #667eea;
  background: white;
}

.search-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}

.filter-btn {
  width: 70rpx;
  height: 70rpx;
  background: #667eea;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-icon {
  font-size: 28rpx;
  color: white;
}

/* 分类标签 */
.category-tabs {
  background: white;
  padding: 20rpx 0;
  white-space: nowrap;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin: 0 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 20rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #667eea;
  color: white;
}

/* 任务列表 */
.task-list {
  flex: 1;
  padding: 20rpx 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.task-item {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  box-sizing: border-box;
  overflow: hidden;
}

.task-item:active {
  transform: scale(0.98);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  gap: 12rpx;
}

.task-title {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  word-wrap: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.task-reward {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  padding: 8rpx 10rpx;
  border-radius: 16rpx;
  text-align: center;
  flex-shrink: 0;
  width: 85rpx;
}

.reward-amount {
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  white-space: nowrap;
}

.task-info {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.task-category,
.task-time,
.task-distance {
  font-size: 24rpx;
  color: #666;
  background: #f0f3ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.task-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-wrap: break-word;
  width: 100%;
  box-sizing: border-box;
}

.task-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 8rpx;
  width: 100%;
  box-sizing: border-box;
}

.location-icon {
  font-size: 24rpx;
  color: #667eea;
}

.location-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
  gap: 16rpx;
}

.publisher-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.publisher-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  border: 2rpx solid #f0f0f0;
}

.publisher-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100rpx;
}

.credit-score {
  font-size: 20rpx;
  color: #52c41a;
  background: #f6ffed;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.task-status {
  flex-shrink: 0;
  min-width: 60rpx;
  text-align: right;
}

.status-text {
  font-size: 20rpx;
  padding: 3rpx 6rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-text.pending {
  background: #e6f7ff;
  color: #1890ff;
}

.status-text.accepted {
  background: #fff2e8;
  color: #fa8c16;
}

.status-text.in_progress {
  background: #f6ffed;
  color: #52c41a;
}

/* 加载状态 */
.load-more,
.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-text,
.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 发布按钮 */
.fab-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 999;
}

.filter-content {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-section {
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

.reward-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.range-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
}

.range-separator {
  font-size: 28rpx;
  color: #999;
}

.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.sort-item {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sort-item.active {
  background: #667eea;
  color: white;
}

.filter-footer {
  display: flex;
  gap: 20rpx;
  padding: 40rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}

.reset-btn {
  background: #f0f0f0;
  color: #666;
}

.apply-btn {
  background: #667eea;
  color: white;
}
