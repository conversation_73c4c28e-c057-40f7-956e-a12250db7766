/* tasks/publish/publish.wxss */
.publish-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.publish-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  background: white;
  border-color: #667eea;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  background: white;
  border-color: #667eea;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选择器 */
.form-picker {
  width: 100%;
  box-sizing: border-box;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.picker-content:active {
  background: #e9ecef;
}

.picker-text {
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 20rpx;
}

/* 报酬输入 */
.reward-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 80rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.reward-input-wrapper:focus-within {
  background: white;
  border-color: #667eea;
}

.currency-symbol {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 600;
}

.reward-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
  width: 0;
  min-width: 0;
}

/* 时间输入 */
.time-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 80rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.time-input-wrapper:focus-within {
  background: white;
  border-color: #667eea;
}

.time-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
  width: 0;
  min-width: 0;
}

.time-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 位置选择 */
.location-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.location-btn {
  flex: 1;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  border: none;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
  box-sizing: border-box;
  gap: 8rpx;
}

.location-btn.secondary {
  background: #e3f2fd;
  color: #1976d2;
}

.location-btn:active {
  background: #e9ecef;
}

.location-btn.secondary:active {
  background: #bbdefb;
}

.location-icon {
  font-size: 24rpx;
}

.location-text {
  font-size: 26rpx;
  white-space: nowrap;
}

.selected-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #f0f9ff;
  border-radius: 12rpx;
  border: 1rpx solid #0ea5e9;
}

.selected-location .location-icon {
  color: #0ea5e9;
}

.selected-location .location-text {
  color: #0c4a6e;
  font-size: 26rpx;
}

/* 地图 */
.map-container {
  margin-top: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.location-map {
  width: 100%;
  height: 300rpx;
}

/* 开关 */
.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-switch {
  transform: scale(0.8);
}

.switch-tip {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-top: -20rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 40rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
}

.preview-btn {
  flex: 1;
  height: 88rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 30rpx;
  border-radius: 44rpx;
  border: none;
}

.publish-btn {
  flex: 2;
  height: 88rpx;
  background: #667eea;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.publish-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}

/* 预览弹窗 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.preview-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.preview-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-body {
  flex: 1;
  padding: 40rpx;
  max-height: 60vh;
}

.preview-item {
  margin-bottom: 30rpx;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.preview-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.preview-value.reward {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b6b;
}

.preview-value.description {
  line-height: 1.6;
}

.preview-footer {
  padding: 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-publish-btn {
  width: 100%;
  height: 80rpx;
  background: #667eea;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 12rpx;
  border: none;
}
