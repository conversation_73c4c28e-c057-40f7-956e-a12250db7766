<!--tasks.wxml-->
<view class="tasks-container">
  <!-- 搜索和筛选栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索任务..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <text class="search-icon" bindtap="onSearch">🔍</text>
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <text class="filter-icon">⚙️</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <scroll-view class="category-tabs" scroll-x>
    <view class="tab-item {{selectedCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
      全部
    </view>
    <view 
      class="tab-item {{selectedCategory === item.value ? 'active' : ''}}" 
      wx:for="{{categories}}" 
      wx:key="value"
      bindtap="selectCategory" 
      data-category="{{item.value}}"
    >
      {{item.label}}
    </view>
  </scroll-view>

  <!-- 任务列表 -->
  <scroll-view 
    class="task-list" 
    scroll-y 
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="loadMore"
  >
    <view class="task-item" wx:for="{{taskList}}" wx:key="id" bindtap="goToTaskDetail" data-id="{{item.id}}">
      <view class="task-header">
        <text class="task-title">{{item.title}}</text>
        <view class="task-reward">
          <text class="reward-amount">¥{{item.reward}}</text>
        </view>
      </view>
      
      <view class="task-info">
        <text class="task-category">{{item.category_label}}</text>
        <text class="task-time">{{item.estimated_hours}}小时</text>
        <text class="task-distance" wx:if="{{item.distance}}">{{item.distance}}km</text>
      </view>
      
      <text class="task-description">{{item.description}}</text>
      
      <view class="task-location">
        <text class="location-icon">📍</text>
        <text class="location-text">{{item.address}}</text>
      </view>
      
      <view class="task-footer">
        <view class="publisher-info">
          <image class="publisher-avatar" src="{{item.publisher_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          <text class="publisher-name">{{item.publisher_nickname}}</text>
          <text class="credit-score">信用{{item.publisher_credit_score}}</text>
        </view>
        <view class="task-status">
          <text class="status-text status-{{item.status}}">{{statusTextMap[item.status] || '未知'}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text">{{isLoading ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && taskList.length > 0}}">
      <text class="no-more-text">没有更多任务了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{taskList.length === 0 && !isLoading}}">
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无任务</text>
      <text class="empty-tip">试试调整筛选条件</text>
    </view>
  </scroll-view>

  <!-- 发布任务按钮 -->
  <view class="fab-btn" bindtap="publishTask">
    <text class="fab-icon">+</text>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal" wx:if="{{showFilterModal}}">
    <view class="filter-content">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <text class="filter-close" bindtap="hideFilter">×</text>
      </view>
      
      <view class="filter-section">
        <text class="filter-label">报酬范围</text>
        <view class="reward-range">
          <input class="range-input" type="number" placeholder="最低" value="{{filterData.minReward}}" bindinput="onFilterInput" data-field="minReward" />
          <text class="range-separator">-</text>
          <input class="range-input" type="number" placeholder="最高" value="{{filterData.maxReward}}" bindinput="onFilterInput" data-field="maxReward" />
        </view>
      </view>

      <view class="filter-section">
        <text class="filter-label">排序方式</text>
        <view class="sort-options">
          <view 
            class="sort-item {{sortBy === item.value ? 'active' : ''}}" 
            wx:for="{{sortOptions}}" 
            wx:key="value"
            bindtap="selectSort" 
            data-sort="{{item.value}}"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <view class="filter-footer">
        <button class="reset-btn" bindtap="resetFilter">重置</button>
        <button class="apply-btn" bindtap="applyFilter">应用</button>
      </view>
    </view>
  </view>
</view>
