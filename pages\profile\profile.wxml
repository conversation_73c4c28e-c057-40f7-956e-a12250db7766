<!--profile.wxml-->
<view class="profile-container">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-info">
      <image 
        class="user-avatar" 
        src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" 
        mode="aspectFill"
        bindtap="changeAvatar"
      ></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || '未设置昵称'}}</text>
        <view class="user-type-badge">
          <text class="type-text">{{userTypeText}}</text>
        </view>
        <view class="user-stats">
          <text class="credit-score">信用分：{{userInfo.credit_score || 0}}</text>
          <text class="verification-level">{{verificationText}}</text>
        </view>
      </view>
      <view class="edit-icon" bindtap="editProfile">
        <text class="icon">✏️</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 个人信息 -->
    <view class="menu-group">
      <text class="group-title">个人信息</text>
      <view class="menu-item" bindtap="editProfile">
        <view class="item-left">
          <text class="item-icon">👤</text>
          <text class="item-title">编辑资料</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="phoneVerification">
        <view class="item-left">
          <text class="item-icon">📱</text>
          <text class="item-title">手机验证</text>
        </view>
        <view class="item-right">
          <text class="item-status {{userInfo.phone ? 'verified' : 'unverified'}}">
            {{userInfo.phone ? '已验证' : '未验证'}}
          </text>
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="realNameAuth">
        <view class="item-left">
          <text class="item-icon">🆔</text>
          <text class="item-title">实名认证</text>
        </view>
        <view class="item-right">
          <text class="item-status {{userInfo.real_name ? 'verified' : 'unverified'}}">
            {{userInfo.real_name ? '已认证' : '未认证'}}
          </text>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 工作相关 -->
    <view class="menu-group">
      <text class="group-title">工作相关</text>
      <view class="menu-item" bindtap="myTasks">
        <view class="item-left">
          <text class="item-icon">📋</text>
          <text class="item-title">我的任务</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="myOrders">
        <view class="item-left">
          <text class="item-icon">📦</text>
          <text class="item-title">我的订单</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="workHistory">
        <view class="item-left">
          <text class="item-icon">📊</text>
          <text class="item-title">工作记录</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 设置 -->
    <view class="menu-group">
      <text class="group-title">设置</text>
      <view class="menu-item" bindtap="settings">
        <view class="item-left">
          <text class="item-icon">⚙️</text>
          <text class="item-title">应用设置</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="help">
        <view class="item-left">
          <text class="item-icon">❓</text>
          <text class="item-title">帮助中心</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
      <view class="menu-item" bindtap="about">
        <view class="item-left">
          <text class="item-icon">ℹ️</text>
          <text class="item-title">关于我们</text>
        </view>
        <view class="item-right">
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
