<!--tasks/publish/publish.wxml-->
<view class="publish-container">
  <scroll-view class="publish-content" scroll-y>
    <!-- 任务基本信息 -->
    <view class="form-section">
      <text class="section-title">任务信息</text>
      
      <view class="form-item">
        <text class="form-label">任务标题 <text class="required">*</text></text>
        <input 
          class="form-input" 
          placeholder="请输入任务标题" 
          value="{{formData.title}}"
          maxlength="50"
          bindinput="onInputChange"
          data-field="title"
        />
      </view>

      <view class="form-item">
        <text class="form-label">任务分类 <text class="required">*</text></text>
        <picker 
          class="form-picker" 
          bindchange="onCategoryChange" 
          value="{{categoryIndex}}" 
          range="{{categories}}"
          range-key="label"
        >
          <view class="picker-content">
            <text class="picker-text">{{categories[categoryIndex].label}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">任务描述 <text class="required">*</text></text>
        <textarea 
          class="form-textarea" 
          placeholder="请详细描述任务内容、要求等信息"
          value="{{formData.description}}"
          maxlength="500"
          bindinput="onInputChange"
          data-field="description"
        ></textarea>
        <text class="char-count">{{formData.description.length}}/500</text>
      </view>
    </view>

    <!-- 任务报酬和时间 -->
    <view class="form-section">
      <text class="section-title">报酬与时间</text>
      
      <view class="form-item">
        <text class="form-label">任务报酬 <text class="required">*</text></text>
        <view class="reward-input-wrapper">
          <text class="currency-symbol">¥</text>
          <input 
            class="reward-input" 
            type="digit"
            placeholder="0"
            value="{{formData.reward}}"
            bindinput="onInputChange"
            data-field="reward"
          />
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">预估工时</text>
        <view class="time-input-wrapper">
          <input 
            class="time-input" 
            type="digit"
            placeholder="0"
            value="{{formData.estimated_hours}}"
            bindinput="onInputChange"
            data-field="estimated_hours"
          />
          <text class="time-unit">小时</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">截止时间</text>
        <picker
          class="form-picker"
          mode="date"
          value="{{formData.deadline}}"
          bindchange="onDeadlineChange"
        >
          <view class="picker-content">
            <text class="picker-text">{{formData.deadline || '请选择截止时间'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 任务地址 -->
    <view class="form-section">
      <text class="section-title">任务地址</text>

      <view class="form-item">
        <text class="form-label">详细地址 <text class="required">*</text></text>
        <input
          class="form-input"
          placeholder="请输入详细地址"
          value="{{formData.address}}"
          maxlength="100"
          bindinput="onInputChange"
          data-field="address"
        />
      </view>

      <view class="form-item">
        <text class="form-label">位置选择</text>
        <view class="location-actions">
          <button class="location-btn" bindtap="chooseLocation">
            <text class="location-icon">📍</text>
            <text class="location-text">地图选择</text>
          </button>
          <button class="location-btn secondary" bindtap="getCurrentLocation">
            <text class="location-icon">🎯</text>
            <text class="location-text">当前位置</text>
          </button>
        </view>
        <view class="selected-location" wx:if="{{formData.latitude && formData.longitude}}">
          <text class="location-icon">📍</text>
          <text class="location-text">{{formData.locationName || '已选择位置'}}</text>
        </view>
      </view>

      <!-- 地图显示 -->
      <view class="map-container" wx:if="{{formData.latitude && formData.longitude}}">
        <map
          class="location-map"
          latitude="{{formData.latitude}}"
          longitude="{{formData.longitude}}"
          markers="{{mapMarkers}}"
          show-location="{{true}}"
        ></map>
      </view>
    </view>

    <!-- 其他设置 -->
    <view class="form-section">
      <text class="section-title">其他设置</text>
      
      <view class="form-item switch-item">
        <text class="form-label">自动接单</text>
        <switch 
          class="form-switch"
          checked="{{formData.auto_accept}}"
          bindchange="onSwitchChange"
          data-field="auto_accept"
        />
      </view>
      <text class="switch-tip">开启后，符合条件的工人可以直接接单，无需您确认</text>
    </view>

    <!-- 底部占位 -->
    <view class="bottom-placeholder"></view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="action-bar">
    <button class="preview-btn" bindtap="previewTask">预览</button>
    <button 
      class="publish-btn" 
      bindtap="publishTask"
      disabled="{{isLoading || !canPublish}}"
    >
      {{isLoading ? '发布中...' : '发布任务'}}
    </button>
  </view>

  <!-- 预览弹窗 -->
  <view class="preview-modal" wx:if="{{showPreview}}">
    <view class="preview-content">
      <view class="preview-header">
        <text class="preview-title">任务预览</text>
        <text class="preview-close" bindtap="closePreview">×</text>
      </view>
      
      <scroll-view class="preview-body" scroll-y>
        <view class="preview-item">
          <text class="preview-label">任务标题</text>
          <text class="preview-value">{{formData.title}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">任务分类</text>
          <text class="preview-value">{{categories[categoryIndex].label}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">任务报酬</text>
          <text class="preview-value reward">¥{{formData.reward}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">任务描述</text>
          <text class="preview-value description">{{formData.description}}</text>
        </view>
        <view class="preview-item">
          <text class="preview-label">任务地址</text>
          <text class="preview-value">{{formData.address}}</text>
        </view>
        <view class="preview-item" wx:if="{{formData.estimated_hours}}">
          <text class="preview-label">预估工时</text>
          <text class="preview-value">{{formData.estimated_hours}}小时</text>
        </view>
        <view class="preview-item" wx:if="{{formData.deadline}}">
          <text class="preview-label">截止时间</text>
          <text class="preview-value">{{formData.deadline}}</text>
        </view>
      </scroll-view>
      
      <view class="preview-footer">
        <button class="confirm-publish-btn" bindtap="confirmPublish">确认发布</button>
      </view>
    </view>
  </view>
</view>
