// index.js
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
const authApi = require('../../utils/auth-api.js');

Page({
  data: {
    motto: '欢迎来到包工头零工平台',
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
    },
    hasUserInfo: false,
    isLoggedIn: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    if (!authApi.isLoggedIn()) {
      // 未登录，跳转到登录页
      authApi.redirectToLogin();
      return;
    }

    try {
      // 尝试刷新用户信息
      const userInfo = await authApi.refreshUserInfo();

      // 已登录，更新页面数据
      this.setData({
        isLoggedIn: true,
        userInfo: userInfo,
        hasUserInfo: true,
        motto: `欢迎回来，${userInfo.nickname || userInfo.nickName}`
      });
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      // 如果刷新失败，使用本地缓存的用户信息
      const localUserInfo = authApi.getLocalUserInfo();
      if (localUserInfo) {
        this.setData({
          isLoggedIn: true,
          userInfo: localUserInfo,
          hasUserInfo: true,
          motto: `欢迎回来，${localUserInfo.nickname || localUserInfo.nickName}`
        });
      } else {
        // 本地也没有用户信息，跳转到登录页
        authApi.redirectToLogin();
      }
    }
  },
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },

  /**
   * 退出登录
   */
  async logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 调用后端登出接口
            await authApi.logout();
          } catch (error) {
            console.error('后端登出失败:', error);
            // 即使后端登出失败，也要清除本地信息
          }

          // 清除本地登录信息并跳转
          authApi.redirectToLogin();
        }
      }
    });
  },

  /**
   * 跳转到任务大厅
   */
  goToTasks() {
    wx.navigateTo({
      url: '/pages/tasks/tasks'
    });
  },

  /**
   * 跳转到我的订单
   */
  goToOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  /**
   * 跳转到个人中心
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    const { nickName } = this.data.userInfo
    this.setData({
      "userInfo.avatarUrl": avatarUrl,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  onInputChange(e) {
    const nickName = e.detail.value
    const { avatarUrl } = this.data.userInfo
    this.setData({
      "userInfo.nickName": nickName,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
})
