<!--pages/orders/orders.wxml-->
<view class="orders-container">
  <!-- 顶部导航 -->
  <view class="orders-header">
    <text class="page-title">我的订单</text>
  </view>

  <!-- 订单状态筛选 -->
  <view class="status-tabs">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}" 
      wx:for="{{statusTabs}}" 
      wx:key="index"
      bindtap="switchTab" 
      data-index="{{index}}"
    >
      <text class="tab-text">{{item.name}}</text>
      <view class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view 
    class="orders-list" 
    scroll-y="true"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="loadMore"
  >
    <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.id}}">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">订单号：{{item.order_number}}</text>
          <text class="order-time">{{item.created_at}}</text>
        </view>
        <view class="order-status {{item.status}}">
          <text class="status-text">{{item.status_text}}</text>
        </view>
      </view>

      <!-- 任务信息 -->
      <view class="task-info">
        <text class="task-title">{{item.task_title}}</text>
        <view class="task-details">
          <text class="task-category">{{item.task_category}}</text>
          <text class="task-time">{{item.estimated_hours}}小时</text>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="address-info" wx:if="{{item.address}}">
        <text class="address-icon">📍</text>
        <text class="address-text">{{item.address}}</text>
      </view>

      <!-- 订单金额 -->
      <view class="order-amount">
        <text class="amount-label">订单金额：</text>
        <text class="amount-value">¥{{item.amount}}</text>
      </view>

      <!-- 订单操作 -->
      <view class="order-actions">
        <button
          class="action-btn secondary"
          wx:if="{{item.showCancelButton}}"
          bindtap="cancelOrder"
          data-id="{{item.id}}"
        >
          取消订单
        </button>
        <button
          class="action-btn primary"
          wx:if="{{item.showContactButton}}"
          bindtap="contactWorker"
          data-id="{{item.id}}"
        >
          联系接单者
        </button>
        <!-- 工人状态流转按钮 -->
        <button
          class="action-btn primary"
          wx:if="{{item.showStartWorkButton}}"
          bindtap="startWork"
          data-id="{{item.id}}"
        >
          开始工作
        </button>
        <button
          class="action-btn primary"
          wx:if="{{item.showCompleteWorkButton}}"
          bindtap="completeWork"
          data-id="{{item.id}}"
        >
          完成工作
        </button>

        <!-- 发布者确认按钮 -->
        <button
          class="action-btn primary"
          wx:if="{{item.showConfirmCompleteButton}}"
          bindtap="confirmComplete"
          data-id="{{item.id}}"
        >
          确认完成
        </button>
        <button
          class="action-btn primary"
          wx:if="{{item.showRateButton}}"
          bindtap="rateOrder"
          data-id="{{item.id}}"
        >
          评价
        </button>
        <button
          class="action-btn secondary"
          wx:if="{{item.showDeleteButton}}"
          bindtap="deleteOrder"
          data-id="{{item.id}}"
        >
          删除
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orderList.length === 0 && !loading}}">
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无订单</text>
      <text class="empty-desc">去任务大厅看看有什么需要帮助的吧</text>
      <button class="goto-tasks-btn" bindtap="goToTasks">去看看</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view>
