/* profile.wxss */
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 用户信息头部 */
.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.user-type-badge {
  margin-bottom: 16rpx;
}

.type-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.user-stats {
  display: flex;
  gap: 20rpx;
}

.credit-score,
.verification-level {
  font-size: 24rpx;
  opacity: 0.9;
}

.edit-icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.edit-icon .icon {
  font-size: 28rpx;
}

/* 功能菜单 */
.menu-section {
  padding: 0 40rpx;
}

.menu-group {
  margin-top: 40rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.group-title {
  display: block;
  padding: 30rpx 30rpx 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 40rpx;
  text-align: center;
}

.item-title {
  font-size: 30rpx;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-status {
  font-size: 26rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.item-status.verified {
  background: #e8f5e8;
  color: #52c41a;
}

.item-status.unverified {
  background: #fff2e8;
  color: #fa8c16;
}

.item-arrow {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}

/* 退出登录按钮 */
.logout-section {
  padding: 60rpx 40rpx 40rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4d4f;
  border-radius: 44rpx;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}
