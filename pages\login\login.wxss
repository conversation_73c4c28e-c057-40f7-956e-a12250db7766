/* login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 顶部Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* 登录表单区域 */
.login-form {
  width: 100%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 60rpx;
}

/* 微信登录按钮 */
.wechat-login-btn {
  width: 100%;
  height: 96rpx;
  background: #07c160;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.wechat-login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

/* 登录成功状态 */
.login-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 24rpx;
  border: 4rpx solid #f0f0f0;
}

.welcome-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 40rpx;
  font-weight: 500;
}

.enter-app-btn {
  width: 100%;
  height: 88rpx;
  background: #667eea;
  border-radius: 44rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 用户类型选择 */
.user-type-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 32rpx;
  text-align: center;
  display: block;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  background: #fafafa;
  transition: all 0.3s ease;
  position: relative;
}

.type-option.selected {
  border-color: #667eea;
  background: #f0f3ff;
}

.type-option.selected::after {
  content: '✓';
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 32rpx;
  font-weight: bold;
}

.type-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
}

.type-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #666;
  margin-left: 84rpx;
  margin-top: -8rpx;
}

.confirm-type-btn {
  width: 100%;
  height: 88rpx;
  background: #ccc;
  border-radius: 44rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.confirm-type-btn.active {
  background: #667eea;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 底部协议区域 */
.agreement-section {
  text-align: center;
  margin-top: auto;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.link {
  color: #fff;
  text-decoration: underline;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 240rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}
