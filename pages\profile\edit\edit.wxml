<!--profile/edit/edit.wxml-->
<view class="edit-container">
  <!-- 头像编辑 -->
  <view class="edit-section avatar-section">
    <text class="section-title">头像</text>
    <view class="avatar-wrapper">
      <image 
        class="avatar" 
        src="{{formData.avatar_url || '/images/default-avatar.png'}}" 
        mode="aspectFill"
      ></image>
      <button class="change-avatar-btn" bindtap="chooseAvatar">更换头像</button>
    </view>
  </view>

  <!-- 基本信息编辑 -->
  <view class="edit-section">
    <text class="section-title">基本信息</text>
    <view class="form-group">
      <view class="form-item">
        <text class="form-label">昵称</text>
        <input 
          class="form-input" 
          value="{{formData.nickname}}" 
          placeholder="请输入昵称" 
          maxlength="20"
          bindinput="onInputChange"
          data-field="nickname"
        />
      </view>

      <view class="form-item">
        <text class="form-label">用户类型</text>
        <picker 
          class="form-picker" 
          bindchange="onUserTypeChange" 
          value="{{userTypeIndex}}" 
          range="{{userTypes}}"
          range-key="label"
        >
          <view class="picker-content">
            <text>{{userTypes[userTypeIndex].label}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">手机号</text>
        <view class="form-input-group">
          <input 
            class="form-input" 
            value="{{formData.phone}}" 
            placeholder="请输入手机号" 
            maxlength="11"
            type="number"
            disabled="{{!!formData.phone}}"
            bindinput="onInputChange"
            data-field="phone"
          />
          <button 
            class="verify-btn {{formData.phone && !formData.phone_verified ? 'active' : ''}}" 
            bindtap="verifyPhone"
            disabled="{{!formData.phone || formData.phone_verified}}"
          >
            {{formData.phone_verified ? '已验证' : '验证'}}
          </button>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">真实姓名</text>
        <input 
          class="form-input" 
          value="{{formData.real_name}}" 
          placeholder="请输入真实姓名" 
          maxlength="20"
          disabled="{{!!formData.real_name}}"
          bindinput="onInputChange"
          data-field="real_name"
        />
      </view>

      <view class="form-item">
        <text class="form-label">身份证号</text>
        <input 
          class="form-input" 
          value="{{formData.id_card}}" 
          placeholder="请输入身份证号" 
          maxlength="18"
          type="idcard"
          disabled="{{!!formData.id_card}}"
          bindinput="onInputChange"
          data-field="id_card"
        />
      </view>

      <view class="form-item">
        <text class="form-label">地址</text>
        <input 
          class="form-input" 
          value="{{formData.address}}" 
          placeholder="请输入地址" 
          maxlength="100"
          bindinput="onInputChange"
          data-field="address"
        />
      </view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="btn-section">
    <button class="save-btn" bindtap="saveProfile">保存修改</button>
    <button class="cancel-btn" bindtap="cancel">取消</button>
  </view>

  <!-- 手机验证码弹窗 -->
  <view class="verify-modal" wx:if="{{showVerifyModal}}">
    <view class="verify-content">
      <view class="verify-header">
        <text class="verify-title">手机验证</text>
        <text class="verify-close" bindtap="closeVerifyModal">×</text>
      </view>
      <view class="verify-body">
        <text class="verify-phone">{{formData.phone}}</text>
        <view class="verify-code-input">
          <input 
            class="code-input" 
            type="number" 
            maxlength="6" 
            placeholder="请输入验证码" 
            bindinput="onCodeInput"
            value="{{verifyCode}}"
          />
          <view class="send-code-btn {{canSendCode ? 'active' : ''}}" bindtap="sendVerifyCode">
            {{sendCodeText}}
          </view>
        </view>
      </view>
      <view class="verify-footer">
        <button class="confirm-btn {{verifyCode.length === 6 ? 'active' : ''}}" bindtap="confirmVerifyCode" disabled="{{verifyCode.length !== 6}}">确认</button>
      </view>
    </view>
  </view>
</view>
