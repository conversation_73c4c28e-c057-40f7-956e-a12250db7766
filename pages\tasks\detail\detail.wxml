<!--tasks/detail/detail.wxml-->
<view class="detail-container">
  <scroll-view class="detail-content" scroll-y>
    <!-- 任务基本信息 -->
    <view class="task-header">
      <view class="task-title-section">
        <text class="task-title">{{taskDetail.title}}</text>
        <view class="task-status">
          <text class="status-text status-{{taskDetail.status}}">{{statusText}}</text>
        </view>
      </view>
      
      <view class="task-reward-section">
        <text class="reward-label">任务报酬</text>
        <text class="reward-amount">¥{{taskDetail.reward}}</text>
      </view>
    </view>

    <!-- 任务信息 -->
    <view class="info-section">
      <view class="info-item">
        <text class="info-label">任务分类</text>
        <text class="info-value">{{taskDetail.category_label}}</text>
      </view>
      <view class="info-item" wx:if="{{taskDetail.estimated_hours}}">
        <text class="info-label">预估工时</text>
        <text class="info-value">{{taskDetail.estimated_hours}}小时</text>
      </view>
      <view class="info-item" wx:if="{{taskDetail.deadline}}">
        <text class="info-label">截止时间</text>
        <text class="info-value">{{deadlineText}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">发布时间</text>
        <text class="info-value">{{createdAtText}}</text>
      </view>
    </view>

    <!-- 任务描述 -->
    <view class="description-section">
      <text class="section-title">任务描述</text>
      <view class="description-text">{{taskDetail.description}}</view>
    </view>

    <!-- 任务地址 -->
    <view class="location-section">
      <text class="section-title">任务地址</text>
      <view class="location-info">
        <text class="location-icon">📍</text>
        <text class="location-text">{{taskDetail.address}}</text>
        <text class="distance-text" wx:if="{{taskDetail.distance}}">距离{{taskDetail.distance}}km</text>
      </view>
      <view class="map-container" wx:if="{{taskDetail.latitude && taskDetail.longitude}}">
        <map
          class="task-map"
          latitude="{{taskDetail.latitude}}"
          longitude="{{taskDetail.longitude}}"
          markers="{{mapMarkers}}"
          show-location="{{true}}"
          bindtap="openMap"
        ></map>
        <view class="map-actions">
          <button class="map-btn primary" bindtap="openMap">导航到此</button>
          <button class="map-btn secondary" bindtap="getMyLocation">我的位置</button>
        </view>
      </view>
    </view>

    <!-- 发布者信息 -->
    <view class="publisher-section">
      <text class="section-title">发布者信息</text>
      <view class="publisher-info">
        <image class="publisher-avatar" src="{{taskDetail.publisher_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="publisher-details">
          <text class="publisher-name">{{taskDetail.publisher_nickname}}</text>
          <view class="publisher-stats">
            <text class="credit-score">信用分：{{taskDetail.publisher_credit_score}}</text>
            <text class="verification-level">{{verificationText}}</text>
          </view>
        </view>
      </view>
      <view class="contact-section">
        <button class="contact-btn" bindtap="contactPublisher">联系发布者</button>
      </view>
    </view>

    <!-- 相关订单信息（如果已接单） -->
    <view class="order-section" wx:if="{{orderInfo}}">
      <text class="section-title">订单信息</text>
      <view class="order-info">
        <view class="order-item">
          <text class="order-label">订单状态</text>
          <text class="order-value order-{{orderInfo.status}}">{{orderStatusText}}</text>
        </view>
        <view class="order-item" wx:if="{{orderInfo.worker_nickname}}">
          <text class="order-label">接单工人</text>
          <text class="order-value">{{orderInfo.worker_nickname}}</text>
        </view>
        <view class="order-item" wx:if="{{orderInfo.accepted_at}}">
          <text class="order-label">接单时间</text>
          <text class="order-value">{{acceptedAtText}}</text>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="bottom-placeholder"></view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="action-bar" wx:if="{{showActionBar}}">
    <button 
      class="action-btn primary" 
      wx:if="{{canAcceptTask}}"
      bindtap="acceptTask"
      disabled="{{isLoading}}"
    >
      {{isLoading ? '处理中...' : '立即接单'}}
    </button>
    
    <button 
      class="action-btn secondary" 
      wx:if="{{canCancelTask}}"
      bindtap="cancelTask"
    >
      取消任务
    </button>
    
    <button 
      class="action-btn primary" 
      wx:if="{{canCompleteTask}}"
      bindtap="completeTask"
    >
      完成任务
    </button>
    
    <button 
      class="action-btn secondary" 
      wx:if="{{canRateTask}}"
      bindtap="rateTask"
    >
      评价任务
    </button>
  </view>
</view>
