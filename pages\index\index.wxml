<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <view class="userinfo" wx:if="{{isLoggedIn}}">
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatar_url || userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickname || userInfo.nickName}}</text>
      <text class="user-type">{{userInfo.user_type === 'worker' ? '工人' : userInfo.user_type === 'merchant' ? '雇主' : '工人&雇主'}}</text>
    </view>

    <view class="usermotto">
      <text class="user-motto">{{motto}}</text>
    </view>

    <!-- 功能区域 -->
    <view class="function-area" wx:if="{{isLoggedIn}}">
      <view class="function-grid">
        <view class="function-item" bindtap="goToTasks">
          <text class="function-icon">📋</text>
          <text class="function-name">任务大厅</text>
        </view>
        <view class="function-item" bindtap="goToOrders">
          <text class="function-icon">📦</text>
          <text class="function-name">我的订单</text>
        </view>
        <view class="function-item" bindtap="goToProfile">
          <text class="function-icon">👤</text>
          <text class="function-name">个人中心</text>
        </view>
        <view class="function-item" bindtap="logout">
          <text class="function-icon">🚪</text>
          <text class="function-name">退出登录</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
