/* profile/edit/edit.wxss */
.edit-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40rpx;
  padding-bottom: 120rpx;
}

/* 编辑区域 */
.edit-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 头像编辑 */
.avatar-section {
  text-align: center;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
}

.change-avatar-btn {
  width: 200rpx;
  height: 60rpx;
  background: #667eea;
  color: white;
  font-size: 26rpx;
  border-radius: 30rpx;
  border: none;
  line-height: 60rpx;
}

/* 表单组 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 80rpx;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: white;
  border-color: #667eea;
}

.form-input[disabled] {
  background: #f0f0f0;
  color: #999;
}

/* 选择器 */
.form-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.picker-arrow {
  color: #999;
  font-size: 20rpx;
}

/* 输入组 */
.form-input-group {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.form-input-group .form-input {
  flex: 1;
}

.verify-btn {
  width: 120rpx;
  height: 60rpx;
  background: #ccc;
  color: white;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  line-height: 60rpx;
}

.verify-btn.active {
  background: #52c41a;
}

.verify-btn[disabled] {
  background: #f0f0f0;
  color: #999;
}

/* 按钮区域 */
.btn-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
}

.save-btn {
  flex: 2;
  height: 88rpx;
  background: #667eea;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 30rpx;
  border-radius: 44rpx;
  border: none;
}

/* 验证码弹窗 */
.verify-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.verify-content {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.verify-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.verify-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.verify-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-body {
  padding: 40rpx;
}

.verify-phone {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.verify-code-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.code-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  letter-spacing: 8rpx;
}

.send-code-btn {
  width: 160rpx;
  height: 80rpx;
  background: #ccc;
  color: white;
  font-size: 24rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-code-btn.active {
  background: #667eea;
}

.verify-footer {
  padding: 0 40rpx 40rpx;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  background: #ccc;
  color: white;
  font-size: 30rpx;
  border-radius: 12rpx;
  border: none;
}

.confirm-btn.active {
  background: #52c41a;
}
