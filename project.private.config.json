{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "b<PERSON><PERSON><PERSON><PERSON>", "setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "bigPackageSizeSupport": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.8.12", "condition": {}}