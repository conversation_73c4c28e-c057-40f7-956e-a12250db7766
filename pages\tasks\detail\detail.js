// pages/tasks/detail/detail.js
const authApi = require('../../../utils/auth-api.js');
const api = require('../../../utils/api.js');

Page({
  data: {
    taskId: null,
    taskDetail: null,
    orderInfo: null,
    mapMarkers: [],
    isLoading: false,
    showActionBar: false,
    canAcceptTask: false,
    canCancelTask: false,
    canCompleteTask: false,
    canRateTask: false,
    currentUserId: null,
    statusText: '',
    deadlineText: '',
    createdAtText: '',
    verificationText: '',
    orderStatusText: '',
    acceptedAtText: ''
  },

  onLoad(options) {
    const taskId = options.id;

    if (!taskId) {
      wx.showToast({
        title: '任务ID不存在',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      taskId: parseInt(taskId)
    });

    this.checkLoginAndLoad();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.taskId) {
      this.loadTaskDetail();
    }
  },



  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!authApi.isLoggedIn()) {
      authApi.redirectToLogin();
      return;
    }

    const userInfo = authApi.getLocalUserInfo();
    this.setData({
      currentUserId: userInfo ? userInfo.id : null
    });

    this.loadTaskDetail();
  },

  /**
   * 加载任务详情
   */
  async loadTaskDetail() {
    if (this.data.isLoading) return;

    this.setData({
      isLoading: true
    });

    try {
      let taskDetail;

      try {
        // 从后台获取任务详情
        const response = await api.get(`/tasks/${this.data.taskId}`);
        console.log('任务详情API响应:', response);

        taskDetail = response.data || response;
      } catch (apiError) {
        console.error('获取任务详情失败:', apiError);
        wx.showToast({
          title: '获取任务详情失败',
          icon: 'none'
        });
        return;
      }

      if (!taskDetail) {
        wx.showToast({
          title: '任务不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 处理任务数据
      const processedTask = {
        ...taskDetail,
        category_label: this.getCategoryLabel(taskDetail.category)
      };

      // 设置地图标记
      const mapMarkers = [];
      if (taskDetail.latitude && taskDetail.longitude) {
        mapMarkers.push({
          id: 1,
          latitude: taskDetail.latitude,
          longitude: taskDetail.longitude,
          title: taskDetail.title
        });
      }

      this.setData({
        taskDetail: processedTask,
        mapMarkers: mapMarkers,
        statusText: this.getStatusText(taskDetail.status),
        deadlineText: this.formatDate(taskDetail.deadline),
        createdAtText: this.formatDate(taskDetail.created_at),
        verificationText: this.getVerificationText(taskDetail.publisher_verification_level)
      });

      // 更新操作按钮状态
      this.updateActionButtons();

    } catch (error) {
      console.error('加载任务详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  /**
   * 更新操作按钮状态
   */
  updateActionButtons() {
    const { taskDetail, currentUserId } = this.data;

    if (!taskDetail || !currentUserId) {
      this.setData({
        showActionBar: false
      });
      return;
    }

    const isPublisher = taskDetail.publisher_id === currentUserId;
    const isWorker = taskDetail.worker_id === currentUserId;
    const taskStatus = taskDetail.status;

    let canAcceptTask = false;
    let canCancelTask = false;
    let canCompleteTask = false;
    let canRateTask = false;

    if (!isPublisher && !isWorker && taskStatus === 'pending') {
      // 非发布者且非接单者，任务待接单
      canAcceptTask = true;
    } else if (isPublisher && taskStatus === 'pending') {
      // 发布者可以取消待接单的任务
      canCancelTask = true;
    } else if (isWorker && taskStatus === 'accepted') {
      // 接单者可以完成已接单的任务
      canCompleteTask = true;
    } else if (isPublisher && taskStatus === 'completed') {
      // 发布者可以评价已完成的任务
      canRateTask = true;
    }

    this.setData({
      showActionBar: canAcceptTask || canCancelTask || canCompleteTask || canRateTask,
      canAcceptTask,
      canCancelTask,
      canCompleteTask,
      canRateTask
    });
  },

  /**
   * 获取分类标签
   */
  getCategoryLabel(categoryValue) {
    const categories = [
      { value: 'cleaning', label: '清洁' },
      { value: 'delivery', label: '配送' },
      { value: 'repair', label: '维修' },
      { value: 'assembly', label: '安装' },
      { value: 'other', label: '其他' }
    ];
    const category = categories.find(cat => cat.value === categoryValue);
    return category ? category.label : '其他';
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取认证级别文本
   */
  getVerificationText(level) {
    const levelMap = {
      'none': '未认证',
      'basic': '基础认证',
      'advanced': '高级认证',
      'premium': '专业认证'
    };
    return levelMap[level] || '未认证';
  },

  /**
   * 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 接单
   */
  async acceptTask() {
    const result = await wx.showModal({
      title: '确认接单',
      content: '确定要接这个任务吗？'
    });

    if (!result.confirm) return;

    try {
      // 调用后端接单API
      const response = await api.post(`/tasks/${this.data.taskId}/accept`, {}, {
        showLoading: true,
        loadingText: '接单中...'
      });

      console.log('接单API响应:', response);

      wx.showToast({
        title: '接单成功',
        icon: 'success'
      });

      // 更新任务状态
      this.setData({
        'taskDetail.status': 'accepted',
        statusText: '已接单'
      });
      this.updateActionButtons();

    } catch (error) {
      console.error('接单失败:', error);
      wx.showToast({
        title: error.message || '接单失败',
        icon: 'none'
      });
    }
  },

  /**
   * 取消任务
   */
  async cancelTask() {
    const result = await wx.showModal({
      title: '确认取消',
      content: '确定要取消这个任务吗？取消后无法恢复。',
      confirmColor: '#ff4d4f'
    });

    if (!result.confirm) return;

    try {
      // 调用后端取消任务API
      const response = await api.post(`/tasks/${this.data.taskId}/cancel`, {}, {
        showLoading: true,
        loadingText: '取消中...'
      });

      console.log('取消任务API响应:', response);

      wx.showToast({
        title: '任务已取消',
        icon: 'success'
      });

      // 更新任务状态
      this.setData({
        'taskDetail.status': 'cancelled',
        statusText: '已取消'
      });
      this.updateActionButtons();

    } catch (error) {
      console.error('取消任务失败:', error);
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      });
    }
  },

  /**
   * 完成任务
   */
  async completeTask() {
    const result = await wx.showModal({
      title: '确认完成',
      content: '确定要标记任务为已完成吗？'
    });

    if (!result.confirm) return;

    try {
      // 调用后端完成任务API
      const response = await api.post(`/tasks/${this.data.taskId}/complete`, {}, {
        showLoading: true,
        loadingText: '完成中...'
      });

      console.log('完成任务API响应:', response);

      wx.showToast({
        title: '任务已完成',
        icon: 'success'
      });

      // 更新任务状态
      this.setData({
        'taskDetail.status': 'completed',
        statusText: '已完成'
      });
      this.updateActionButtons();

    } catch (error) {
      console.error('完成任务失败:', error);
      wx.showToast({
        title: error.message || '完成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 评价任务
   */
  rateTask() {
    const taskId = this.data.taskId;
    wx.navigateTo({
      url: `/pages/tasks/rate/rate?taskId=${taskId}`
    });
  },

  /**
   * 联系发布者
   */
  contactPublisher() {
    wx.showModal({
      title: '联系发布者',
      content: '您可以通过以下方式联系发布者：\n\n1. 平台内消息\n2. 电话联系\n\n注意保护个人隐私安全',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 打开地图导航
   */
  openMap() {
    const { latitude, longitude, address } = this.data.taskDetail;
    if (!latitude || !longitude) {
      wx.showToast({
        title: '位置信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.openLocation({
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      name: '任务地点',
      address: address || '',
      scale: 18
    });
  },

  /**
   * 获取我的位置
   */
  getMyLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res;
        const taskLat = parseFloat(this.data.taskDetail.latitude);
        const taskLng = parseFloat(this.data.taskDetail.longitude);

        // 计算距离
        const distance = this.calculateDistance(latitude, longitude, taskLat, taskLng);

        wx.showToast({
          title: `距离任务地点约${distance.toFixed(1)}km`,
          icon: 'none',
          duration: 3000
        });

        // 更新地图标记，显示我的位置和任务位置
        this.setData({
          mapMarkers: [
            {
              id: 1,
              latitude: taskLat,
              longitude: taskLng,
              iconPath: '/images/marker-task.png',
              width: 30,
              height: 30,
              title: '任务地点'
            },
            {
              id: 2,
              latitude: latitude,
              longitude: longitude,
              iconPath: '/images/marker-my.png',
              width: 30,
              height: 30,
              title: '我的位置'
            }
          ]
        });
      },
      fail: (error) => {
        console.error('获取位置失败:', error);
        wx.showToast({
          title: '获取位置失败，请检查位置权限',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 计算两点间距离（公里）
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
})