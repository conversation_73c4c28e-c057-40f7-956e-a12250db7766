/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 40rpx;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}

.userinfo-nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.user-type {
  font-size: 24rpx;
  color: #666;
  background: #f0f3ff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #667eea;
}

.usermotto {
  text-align: center;
  margin-bottom: 40rpx;
}

.user-motto {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

/* 功能区域 */
.function-area {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.function-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.function-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.function-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 保留原有样式以兼容 */
.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
