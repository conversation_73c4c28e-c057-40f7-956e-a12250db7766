/**
 * 认证相关API
 * 处理用户登录、注册、个人信息等接口
 */

const api = require('./api.js');

/**
 * 微信登录
 * @param {Object} loginData - 登录数据
 * @param {string} loginData.code - 微信授权码
 * @param {string} loginData.nickname - 用户昵称
 * @param {string} loginData.avatar_url - 用户头像URL
 * @returns {Promise} 登录结果
 */
function wechatLogin(loginData) {
  console.log('调用微信登录API:', loginData);
  
  return api.post('/auth/wechat-login', loginData, {
    needAuth: false,
    showLoading: true,
    loadingText: '登录中...'
  });
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
function getUserProfile() {
  console.log('获取用户信息');
  
  return api.get('/auth/profile', {}, {
    needAuth: true
  });
}

/**
 * 更新用户信息
 * @param {Object} updateData - 更新数据
 * @returns {Promise} 更新结果
 */
function updateUserProfile(updateData) {
  console.log('更新用户信息:', updateData);
  
  return api.post('/auth/update-profile', updateData, {
    needAuth: true,
    showLoading: true,
    loadingText: '更新中...'
  });
}

/**
 * 发送手机验证码
 * @param {string} phone - 手机号
 * @returns {Promise} 发送结果
 */
function sendSmsCode(phone) {
  console.log('发送验证码:', phone);
  
  return api.post('/auth/send-sms', { phone }, {
    needAuth: true,
    showLoading: true,
    loadingText: '发送中...'
  });
}

/**
 * 验证手机号
 * @param {Object} verifyData - 验证数据
 * @param {string} verifyData.phone - 手机号
 * @param {string} verifyData.code - 验证码
 * @returns {Promise} 验证结果
 */
function verifyPhone(verifyData) {
  console.log('验证手机号:', verifyData);
  
  return api.post('/auth/verify-phone', verifyData, {
    needAuth: true,
    showLoading: true,
    loadingText: '验证中...'
  });
}

/**
 * 登出
 * @returns {Promise} 登出结果
 */
function logout() {
  console.log('用户登出');
  
  return api.post('/auth/logout', {}, {
    needAuth: true
  });
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('access_token');
  const userInfo = wx.getStorageSync('userInfo');
  return !!(token && userInfo);
}

/**
 * 获取本地存储的用户信息
 * @returns {Object|null} 用户信息
 */
function getLocalUserInfo() {
  try {
    return wx.getStorageSync('userInfo');
  } catch (error) {
    console.error('获取本地用户信息失败:', error);
    return null;
  }
}

/**
 * 获取本地存储的访问令牌
 * @returns {string|null} 访问令牌
 */
function getLocalToken() {
  try {
    return wx.getStorageSync('access_token');
  } catch (error) {
    console.error('获取本地令牌失败:', error);
    return null;
  }
}

/**
 * 保存登录信息到本地
 * @param {Object} loginResponse - 登录响应数据
 * @param {string} loginResponse.access_token - 访问令牌
 * @param {Object} loginResponse.user - 用户信息
 */
function saveLoginInfo(loginResponse) {
  try {
    const { access_token, user } = loginResponse;
    
    wx.setStorageSync('access_token', access_token);
    wx.setStorageSync('userInfo', user);
    
    console.log('登录信息已保存到本地');
  } catch (error) {
    console.error('保存登录信息失败:', error);
    throw new Error('保存登录信息失败');
  }
}

/**
 * 清除本地登录信息
 */
function clearLoginInfo() {
  try {
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('userInfo');
    console.log('本地登录信息已清除');
  } catch (error) {
    console.error('清除登录信息失败:', error);
  }
}

/**
 * 强制跳转到登录页
 */
function redirectToLogin() {
  clearLoginInfo();
  wx.redirectTo({
    url: '/pages/login/login'
  });
}

/**
 * 检查并刷新用户信息
 * @returns {Promise} 用户信息
 */
async function refreshUserInfo() {
  try {
    if (!isLoggedIn()) {
      throw new Error('用户未登录');
    }
    
    const userInfo = await getUserProfile();
    wx.setStorageSync('userInfo', userInfo);
    return userInfo;
  } catch (error) {
    console.error('刷新用户信息失败:', error);
    if (error.message.includes('登录') || error.message.includes('401')) {
      redirectToLogin();
    }
    throw error;
  }
}

// 导出认证API
module.exports = {
  // 登录相关
  wechatLogin,
  logout,
  isLoggedIn,
  redirectToLogin,
  
  // 用户信息
  getUserProfile,
  updateUserProfile,
  refreshUserInfo,
  
  // 手机验证
  sendSmsCode,
  verifyPhone,
  
  // 本地存储
  getLocalUserInfo,
  getLocalToken,
  saveLoginInfo,
  clearLoginInfo
};
