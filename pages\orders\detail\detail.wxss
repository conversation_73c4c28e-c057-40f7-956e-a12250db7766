/* pages/orders/detail/detail.wxss */
.detail-container {
  min-height: 100vh;
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.detail-content {
  flex: 1;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 订单头部 */
.order-header {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.order-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  width: 100%;
  flex-wrap: wrap;
  gap: 10rpx;
}

.order-number {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  flex: 1;
  min-width: 200rpx;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
}

.status-in_progress {
  background: #cce5ff;
  color: #004085;
}

.status-worker_completed {
  background: #fff3cd;
  color: #856404;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.order-type-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.type-label {
  font-size: 26rpx;
  color: #666;
}

.type-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 任务信息 */
.task-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  width: 100%;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.link-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  flex-shrink: 0;
}

.task-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.task-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin: 16rpx 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
}

.meta-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.meta-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 地址信息 */
.address-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.address-info {
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.address-icon {
  font-size: 28rpx;
  color: #667eea;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.address-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
  word-break: break-all;
}

.task-map {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 金额信息 */
.amount-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.amount-info {
  margin-top: 10rpx;
}

/* 参与人员信息 */
.participants-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.participant-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.participant-item:last-child {
  border-bottom: none;
}

.participant-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 120rpx;
}

.participant-info {
  flex: 1;
  text-align: right;
}

.participant-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.participant-phone {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 时间线信息 */
.timeline-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.amount-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b6b;
}

/* 底部操作栏 */
.action-bar {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  min-width: 140rpx;
  text-align: center;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.action-btn.danger {
  background: #ff4757;
  color: white;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
