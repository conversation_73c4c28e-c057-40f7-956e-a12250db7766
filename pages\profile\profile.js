// pages/profile/profile.js
const authApi = require('../../utils/auth-api.js');

Page({
  data: {
    userInfo: null,
    isLoading: false,
    userTypeText: '未设置',
    verificationText: '未认证'
  },

  onLoad(options) {
    this.loadUserInfo();
  },

  onShow() {
    // 每次显示页面时刷新用户信息
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      // 检查登录状态
      if (!authApi.isLoggedIn()) {
        authApi.redirectToLogin();
        return;
      }

      // 获取本地用户信息
      const localUserInfo = authApi.getLocalUserInfo();
      if (localUserInfo) {
        this.setData({
          userInfo: localUserInfo,
          userTypeText: this.getUserTypeText(localUserInfo.user_type),
          verificationText: this.getVerificationText(localUserInfo.verification_level)
        });
      }

      // 尝试从服务器刷新用户信息
      const userInfo = await authApi.refreshUserInfo();
      this.setData({
        userInfo: userInfo,
        userTypeText: this.getUserTypeText(userInfo.user_type),
        verificationText: this.getVerificationText(userInfo.verification_level)
      });

    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 如果刷新失败但有本地数据，继续使用本地数据
      if (!this.data.userInfo) {
        wx.showToast({
          title: '加载用户信息失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 获取用户类型文本
   */
  getUserTypeText(userType) {
    const typeMap = {
      'worker': '工人',
      'merchant': '雇主',
      'both': '工人&雇主'
    };
    return typeMap[userType] || '未设置';
  },

  /**
   * 获取认证级别文本
   */
  getVerificationText(level) {
    const levelMap = {
      'none': '未认证',
      'basic': '基础认证',
      'advanced': '高级认证',
      'premium': '专业认证'
    };
    return levelMap[level] || '未认证';
  },

  /**
   * 更换头像
   */
  changeAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        // TODO: 上传头像到服务器
        wx.showToast({
          title: '头像上传功能开发中',
          icon: 'none'
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
      }
    });
  },

  /**
   * 编辑个人资料
   */
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },

  /**
   * 手机验证
   */
  phoneVerification() {
    if (this.data.userInfo && this.data.userInfo.phone) {
      wx.showModal({
        title: '手机已验证',
        content: `您的手机号 ${this.data.userInfo.phone} 已通过验证`,
        showCancel: false,
        confirmText: '我知道了'
      });
    } else {
      wx.navigateTo({
        url: '/pages/profile/edit/edit'
      });
    }
  },

  /**
   * 实名认证
   */
  realNameAuth() {
    if (this.data.userInfo && this.data.userInfo.real_name) {
      wx.showModal({
        title: '已实名认证',
        content: `您已完成实名认证`,
        showCancel: false,
        confirmText: '我知道了'
      });
    } else {
      wx.showModal({
        title: '实名认证',
        content: '请先在编辑资料中填写真实姓名和身份证号',
        confirmText: '去填写',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/profile/edit/edit'
            });
          }
        }
      });
    }
  },

  /**
   * 我的任务
   */
  myTasks() {
    wx.showToast({
      title: '我的任务功能开发中',
      icon: 'none'
    });
  },

  /**
   * 我的订单
   */
  myOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  /**
   * 工作记录
   */
  workHistory() {
    wx.showToast({
      title: '工作记录功能开发中',
      icon: 'none'
    });
  },

  /**
   * 应用设置
   */
  settings() {
    const settingsItems = [
      '消息通知设置',
      '隐私设置',
      '清除缓存',
      '检查更新'
    ];

    wx.showActionSheet({
      itemList: settingsItems,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.notificationSettings();
            break;
          case 1:
            this.privacySettings();
            break;
          case 2:
            this.clearCache();
            break;
          case 3:
            this.checkUpdate();
            break;
        }
      }
    });
  },

  /**
   * 消息通知设置
   */
  notificationSettings() {
    wx.showModal({
      title: '消息通知设置',
      content: '您可以在系统设置中管理小程序的通知权限',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting();
        }
      }
    });
  },

  /**
   * 隐私设置
   */
  privacySettings() {
    wx.showModal({
      title: '隐私设置',
      content: '我们严格保护您的个人隐私，详情请查看隐私政策',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 清除缓存
   */
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除应用缓存吗？这不会影响您的个人数据',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync();
            // 重新设置必要的登录信息
            const userInfo = this.data.userInfo;
            const token = authApi.getLocalToken();
            if (userInfo && token) {
              wx.setStorageSync('userInfo', userInfo);
              wx.setStorageSync('access_token', token);
            }

            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '发现新版本，是否立即更新？',
          success: (res) => {
            if (res.confirm) {
              updateManager.onUpdateReady(() => {
                updateManager.applyUpdate();
              });
            }
          }
        });
      } else {
        wx.showToast({
          title: '已是最新版本',
          icon: 'success'
        });
      }
    });

    updateManager.onUpdateFailed(() => {
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    });
  },

  /**
   * 帮助中心
   */
  help() {
    const helpItems = [
      '常见问题',
      '使用教程',
      '联系客服',
      '意见反馈'
    ];

    wx.showActionSheet({
      itemList: helpItems,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showFAQ();
            break;
          case 1:
            this.showTutorial();
            break;
          case 2:
            this.contactService();
            break;
          case 3:
            this.feedback();
            break;
        }
      }
    });
  },

  /**
   * 常见问题
   */
  showFAQ() {
    wx.showModal({
      title: '常见问题',
      content: 'Q: 如何发布任务？\nA: 在任务大厅点击发布按钮\n\nQ: 如何接单？\nA: 在任务列表中选择合适的任务\n\nQ: 如何提现？\nA: 在个人中心查看收益详情',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 使用教程
   */
  showTutorial() {
    wx.showModal({
      title: '使用教程',
      content: '1. 完善个人信息\n2. 选择身份类型\n3. 浏览或发布任务\n4. 完成工作获得收益',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00\n\n或者您可以通过意见反馈功能联系我们',
      confirmText: '拨打电话',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  /**
   * 意见反馈
   */
  feedback() {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的宝贵意见！请通过以下方式联系我们：\n\n邮箱：<EMAIL>\n微信群：扫描二维码加入',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 关于我们
   */
  about() {
    wx.showModal({
      title: '关于我们',
      content: '包工头零工平台 v1.0.0\n\n连接雇主与工人，让工作更简单\n\n我们致力于为用户提供安全、便捷的零工服务平台，帮助更多人实现灵活就业。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 退出登录
   */
  async logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 调用后端登出接口
            await authApi.logout();
          } catch (error) {
            console.error('后端登出失败:', error);
            // 即使后端登出失败，也要清除本地信息
          }
          
          // 清除本地登录信息并跳转
          authApi.redirectToLogin();
        }
      }
    });
  }
});
