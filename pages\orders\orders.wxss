/* pages/orders/orders.wxss */
.orders-container {
  height: 100vh;
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.orders-header {
  background: white;
  padding: 20rpx 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 状态筛选标签 */
.status-tabs {
  display: flex;
  background: white;
  padding: 0 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  position: relative;
  gap: 8rpx;
}

.tab-item.active {
  color: #667eea;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #667eea;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-count {
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

/* 订单列表 */
.orders-list {
  flex: 1;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.order-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.accepted {
  background: #d4edda;
  color: #155724;
}

.order-status.in_progress {
  background: #cce5ff;
  color: #004085;
}

.order-status.completed {
  background: #d1ecf1;
  color: #0c5460;
}

.order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

/* 任务信息 */
.task-info {
  margin-bottom: 20rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.task-details {
  display: flex;
  gap: 20rpx;
}

.task-category,
.task-time {
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 地址信息 */
.address-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.address-icon {
  font-size: 24rpx;
  color: #667eea;
}

.address-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}

/* 订单金额 */
.order-amount {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #333;
}

.amount-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-left: 8rpx;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
  flex-wrap: wrap;
  width: 100%;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  flex-shrink: 0;
  min-width: 120rpx;
  text-align: center;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.goto-tasks-btn {
  background: #667eea;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

/* 加载状态 */
.loading-more {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}
