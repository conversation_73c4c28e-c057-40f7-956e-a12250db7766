// pages/tasks/tasks.js
const authApi = require('../../utils/auth-api.js');
const api = require('../../utils/api.js');

Page({
  data: {
    taskList: [],
    searchKeyword: '',
    selectedCategory: '',
    categories: [
      { value: 'cleaning', label: '清洁' },
      { value: 'delivery', label: '配送' },
      { value: 'repair', label: '维修' },
      { value: 'assembly', label: '安装' },
      { value: 'other', label: '其他' }
    ],
    statusTextMap: {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    isLoading: false,
    isRefreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 筛选相关
    showFilterModal: false,
    filterData: {
      minReward: '',
      maxReward: ''
    },
    sortBy: 'created_at',
    sortOptions: [
      { value: 'created_at', label: '最新发布' },
      { value: 'reward', label: '报酬最高' },
      { value: 'distance', label: '距离最近' }
    ]
  },

  onLoad(options) {
    this.checkLoginAndLoad();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.refreshTasks();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!authApi.isLoggedIn()) {
      authApi.redirectToLogin();
      return;
    }
    this.loadTasks();
  },

  /**
   * 加载任务列表
   */
  async loadTasks(isLoadMore = false) {
    if (this.data.isLoading) return;

    this.setData({
      isLoading: true
    });

    try {
      const params = {
        page: isLoadMore ? this.data.page + 1 : 1,
        size: this.data.pageSize,
        status: 'pending' // 只显示待接单的任务
      };

      console.log('加载任务参数:', params);

      let response;
      let processedTasks = [];

      try {
        response = await api.get('/tasks', params);
        console.log('API响应:', response);

        // 处理任务数据，添加分类标签
        const tasks = response.data || response.tasks || response || [];
        if (Array.isArray(tasks)) {
          const currentUserId = authApi.getLocalUserInfo()?.id;
          processedTasks = tasks
            .filter(task => {
              // 只显示待接单的任务，且不是自己发布的任务
              return task.status === 'pending' && task.publisher_id !== currentUserId;
            })
            .map(task => ({
              ...task,
              category_label: this.getCategoryLabel(task.category)
            }));
        }
      } catch (apiError) {
        console.error('API请求失败:', apiError);
        // API失败时显示空列表
        processedTasks = [];
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }

      // 客户端分类过滤
      if (this.data.selectedCategory && this.data.selectedCategory.trim()) {
        processedTasks = processedTasks.filter(task => {
          return task.category === this.data.selectedCategory;
        });
      }

      // 客户端搜索过滤
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.toLowerCase().trim();
        processedTasks = processedTasks.filter(task => {
          const titleMatch = task.title && task.title.toLowerCase().includes(keyword);
          const descMatch = task.description && task.description.toLowerCase().includes(keyword);
          const addressMatch = task.address && task.address.toLowerCase().includes(keyword);
          return titleMatch || descMatch || addressMatch;
        });
      }

      if (isLoadMore) {
        this.setData({
          taskList: [...this.data.taskList, ...processedTasks],
          page: this.data.page + 1,
          hasMore: response && (response.has_more || response.hasMore || (processedTasks.length === this.data.pageSize))
        });
      } else {
        this.setData({
          taskList: processedTasks,
          page: 1,
          hasMore: response && (response.has_more || response.hasMore || (processedTasks.length === this.data.pageSize))
        });
      }

    } catch (error) {
      console.error('加载任务失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false,
        isRefreshing: false
      });
    }
  },

  /**
   * 获取分类标签
   */
  getCategoryLabel(categoryValue) {
    const category = this.data.categories.find(cat => cat.value === categoryValue);
    return category ? category.label : '其他';
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '待接单',
      'accepted': '已接单',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    console.log('执行搜索，关键词:', this.data.searchKeyword);
    this.refreshTasks();
  },

  /**
   * 选择分类
   */
  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    console.log('选择分类:', category);
    this.setData({
      selectedCategory: category
    });
    this.refreshTasks();
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({
      isRefreshing: true
    });
    this.loadTasks();
  },

  /**
   * 加载更多
   */
  loadMore() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadTasks(true);
    }
  },

  /**
   * 刷新任务列表
   */
  refreshTasks() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadTasks();
  },

  /**
   * 显示筛选弹窗
   */
  showFilter() {
    this.setData({
      showFilterModal: true
    });
  },

  /**
   * 隐藏筛选弹窗
   */
  hideFilter() {
    this.setData({
      showFilterModal: false
    });
  },

  /**
   * 筛选条件输入
   */
  onFilterInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`filterData.${field}`]: value
    });
  },

  /**
   * 选择排序方式
   */
  selectSort(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      sortBy: sort
    });
  },

  /**
   * 重置筛选条件
   */
  resetFilter() {
    this.setData({
      filterData: {
        minReward: '',
        maxReward: ''
      },
      sortBy: 'created_at'
    });
  },

  /**
   * 应用筛选条件
   */
  applyFilter() {
    this.setData({
      showFilterModal: false
    });
    this.loadTasks();
  },

  /**
   * 跳转到任务详情
   */
  goToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/tasks/detail/detail?id=${taskId}`
    });
  },

  /**
   * 发布任务
   */
  publishTask() {
    wx.navigateTo({
      url: '/pages/tasks/publish/publish'
    });
  }
});
