// pages/login/login.js
const authApi = require('../../utils/auth-api.js');

Page({
  data: {
    isLoggedIn: false,
    isLoading: false,
    loadingText: '登录中...',
    userInfo: null,
    showUserTypeSelection: false,
    selectedUserType: '',
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },

  onLoad(options) {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    if (authApi.isLoggedIn()) {
      const userInfo = authApi.getLocalUserInfo();
      this.setData({
        isLoggedIn: true,
        userInfo: userInfo
      });
    }
  },

  /**
   * 微信登录
   */
  onWechatLogin(e) {
    console.log('微信登录事件:', e);

    this.setData({
      isLoading: true,
      loadingText: '正在获取微信授权...'
    });

    // 获取微信登录code
    wx.login({
      success: (res) => {
        console.log('wx.login success:', res);
        if (res.code) {
          // 在开发者工具中，直接使用模拟数据
          if (typeof __wxConfig !== 'undefined' && __wxConfig.envVersion === 'develop') {
            console.log('开发环境，使用模拟数据');
            this.performWechatLogin(res.code, {
              nickName: '测试用户',
              avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
            });
            return;
          }

          // 获取用户信息
          if (wx.getUserProfile) {
            wx.getUserProfile({
              desc: '用于完善用户资料',
              success: (profileRes) => {
                console.log('getUserProfile success:', profileRes);
                this.performWechatLogin(res.code, profileRes.userInfo);
              },
              fail: (err) => {
                console.log('getUserProfile fail:', err);
                // 如果用户拒绝授权，使用默认数据
                this.performWechatLogin(res.code, {
                  nickName: '微信用户',
                  avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
                });
              }
            });
          } else {
            // 兼容旧版本
            console.log('使用兼容模式');
            this.performWechatLogin(res.code, {
              nickName: '微信用户',
              avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
            });
          }
        } else {
          this.handleLoginError('获取微信授权失败');
        }
      },
      fail: (err) => {
        console.log('wx.login fail:', err);
        this.handleLoginError('微信登录失败');
      }
    });
  },

  /**
   * 执行微信登录
   */
  async performWechatLogin(code, userInfo) {
    console.log('performWechatLogin called with:', { code, userInfo });

    this.setData({
      loadingText: '正在登录...'
    });

    try {
      // 调用后端API进行登录
      const loginData = {
        code: code,
        nickname: userInfo.nickName,
        avatar_url: userInfo.avatarUrl
      };

      const loginResponse = await authApi.wechatLogin(loginData);
      console.log('登录成功:', loginResponse);

      // 保存登录信息
      authApi.saveLoginInfo(loginResponse);

      this.setData({
        isLoading: false,
        isLoggedIn: true,
        userInfo: loginResponse.user,
        showUserTypeSelection: !loginResponse.user.user_type
      });

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('登录失败:', error);
      this.handleLoginError(error.message || '登录失败，请重试');
    }
  },

  /**
   * 处理登录错误
   */
  handleLoginError(message) {
    this.setData({
      isLoading: false
    });
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 选择用户类型
   */
  selectUserType(e) {
    const userType = e.currentTarget.dataset.type;
    this.setData({
      selectedUserType: userType
    });
  },

  /**
   * 确认用户类型
   */
  async confirmUserType() {
    if (!this.data.selectedUserType) {
      wx.showToast({
        title: '请选择身份类型',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      loadingText: '正在更新用户信息...'
    });

    try {
      // 调用后端API更新用户类型
      const updateData = {
        user_type: this.data.selectedUserType
      };

      const updatedUserInfo = await authApi.updateUserProfile(updateData);
      console.log('用户类型更新成功:', updatedUserInfo);

      // 更新本地存储
      wx.setStorageSync('userInfo', updatedUserInfo);

      this.setData({
        isLoading: false,
        userInfo: updatedUserInfo,
        showUserTypeSelection: false
      });

      wx.showToast({
        title: '身份设置成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('更新用户类型失败:', error);
      this.setData({
        isLoading: false
      });

      wx.showToast({
        title: error.message || '更新失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 进入应用
   */
  enterApp() {
    // 跳转到首页
    wx.redirectTo({
      url: '/pages/index/index'
    });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
});
