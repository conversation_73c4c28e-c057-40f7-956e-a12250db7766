<!--pages/orders/detail/detail.wxml-->
<view class="detail-container">
  <scroll-view class="detail-content" scroll-y>
    <!-- 订单基本信息 -->
    <view class="order-header">
      <view class="order-title-section">
        <text class="order-number">订单号：{{orderDetail.order_number}}</text>
        <view class="order-status">
          <text class="status-text status-{{orderDetail.status}}">{{statusText}}</text>
        </view>
      </view>
      
      <view class="order-type-section">
        <text class="type-label">订单类型</text>
        <text class="type-text">{{orderTypeText}}</text>
      </view>
    </view>

    <!-- 任务信息 -->
    <view class="task-section">
      <view class="section-header">
        <text class="section-title">任务信息</text>
        <button class="link-btn" bindtap="goToTaskDetail">查看详情</button>
      </view>
      
      <view class="task-info">
        <text class="task-title">{{orderDetail.task_title}}</text>
        <view class="task-meta">
          <view class="meta-item">
            <text class="meta-icon">🔧</text>
            <text class="meta-text">{{orderDetail.task_category}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">⏰</text>
            <text class="meta-text">{{orderDetail.estimated_hours}}小时</text>
          </view>
        </view>
        <view class="task-description" wx:if="{{orderDetail.task_description}}">
          <text class="description-text">{{orderDetail.task_description}}</text>
        </view>
      </view>

      <!-- 任务地址 -->
      <view class="address-section" wx:if="{{orderDetail.task_address}}">
        <view class="address-info">
          <text class="address-icon">📍</text>
          <text class="address-text">{{orderDetail.task_address}}</text>
        </view>
        
        <!-- 地图显示 -->
        <map 
          wx:if="{{mapMarkers.length > 0}}"
          class="task-map"
          latitude="{{mapMarkers[0].latitude}}"
          longitude="{{mapMarkers[0].longitude}}"
          markers="{{mapMarkers}}"
          show-location="{{true}}"
          scale="16"
        ></map>
      </view>
    </view>

    <!-- 订单金额 -->
    <view class="amount-section">
      <text class="section-title">订单金额</text>
      <view class="amount-info">
        <text class="amount-value">¥{{orderDetail.amount}}</text>
      </view>
    </view>

    <!-- 参与人员信息 -->
    <view class="participants-section">
      <text class="section-title">参与人员</text>
      
      <!-- 发布者信息 -->
      <view class="participant-item">
        <text class="participant-label">发布者</text>
        <view class="participant-info">
          <text class="participant-name">{{orderDetail.publisher_nickname}}</text>
          <text class="participant-phone" wx:if="{{orderDetail.publisher_phone}}">{{orderDetail.publisher_phone}}</text>
        </view>
      </view>

      <!-- 接单者信息 -->
      <view class="participant-item" wx:if="{{orderDetail.worker_nickname}}">
        <text class="participant-label">接单者</text>
        <view class="participant-info">
          <text class="participant-name">{{orderDetail.worker_nickname}}</text>
          <text class="participant-phone" wx:if="{{orderDetail.worker_phone}}">{{orderDetail.worker_phone}}</text>
        </view>
      </view>
    </view>

    <!-- 订单时间信息 -->
    <view class="timeline-section">
      <text class="section-title">订单时间线</text>
      
      <view class="timeline-list">
        <view class="timeline-item">
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <text class="timeline-title">订单创建</text>
            <text class="timeline-time">{{createdAtText}}</text>
          </view>
        </view>

        <view class="timeline-item" wx:if="{{acceptedAtText}}">
          <view class="timeline-dot active"></view>
          <view class="timeline-content">
            <text class="timeline-title">订单接受</text>
            <text class="timeline-time">{{acceptedAtText}}</text>
          </view>
        </view>

        <view class="timeline-item" wx:if="{{completedAtText}}">
          <view class="timeline-dot completed"></view>
          <view class="timeline-content">
            <text class="timeline-title">订单完成</text>
            <text class="timeline-time">{{completedAtText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 评价信息 -->
    <view class="rating-section" wx:if="{{orderDetail.rating}}">
      <text class="section-title">订单评价</text>
      <view class="rating-info">
        <view class="rating-stars">
          <text class="star {{index < orderDetail.rating ? 'filled' : ''}}" 
                wx:for="{{[1,2,3,4,5]}}" 
                wx:key="index">★</text>
        </view>
        <text class="rating-comment" wx:if="{{orderDetail.rating_comment}}">{{orderDetail.rating_comment}}</text>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="bottom-placeholder"></view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="action-bar" wx:if="{{showActionBar}}">
    <button 
      class="action-btn secondary" 
      wx:if="{{canCancelOrder}}"
      bindtap="cancelOrder"
    >
      取消订单
    </button>
    
    <button 
      class="action-btn primary" 
      wx:if="{{canContactWorker}}"
      bindtap="contactUser"
    >
      联系对方
    </button>
    
    <!-- 工人状态流转按钮 -->
    <button
      class="action-btn primary"
      wx:if="{{canStartWork}}"
      bindtap="startWork"
    >
      开始工作
    </button>

    <button
      class="action-btn primary"
      wx:if="{{canCompleteWork}}"
      bindtap="completeWork"
    >
      完成工作
    </button>

    <!-- 发布者确认按钮 -->
    <button
      class="action-btn primary"
      wx:if="{{canConfirmComplete}}"
      bindtap="confirmComplete"
    >
      确认完成
    </button>
    
    <button 
      class="action-btn primary" 
      wx:if="{{canRateOrder}}"
      bindtap="rateOrder"
    >
      评价订单
    </button>
    
    <button 
      class="action-btn secondary" 
      wx:if="{{canDeleteOrder}}"
      bindtap="deleteOrder"
    >
      删除订单
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view>
