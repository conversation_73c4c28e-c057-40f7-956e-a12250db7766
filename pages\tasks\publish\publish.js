// pages/tasks/publish/publish.js
const authApi = require('../../../utils/auth-api.js');
const api = require('../../../utils/api.js');

Page({
  data: {
    formData: {
      title: '',
      description: '',
      category: 'cleaning',
      reward: '',
      estimated_hours: '',
      deadline: '',
      address: '',
      latitude: null,
      longitude: null,
      locationName: '',
      auto_accept: true
    },
    categories: [
      { value: 'cleaning', label: '清洁' },
      { value: 'delivery', label: '配送' },
      { value: 'repair', label: '维修' },
      { value: 'assembly', label: '安装' },
      { value: 'other', label: '其他' }
    ],
    categoryIndex: 0,
    mapMarkers: [],
    showPreview: false,
    isLoading: false,
    canPublish: false,
    currentDate: ''
  },

  onLoad() {
    this.checkLoginStatus();
    this.validateForm();
    this.initCurrentDate();
  },

  /**
   * 初始化当前日期
   */
  initCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    this.setData({
      currentDate: `${year}-${month}-${day}`
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    if (!authApi.isLoggedIn()) {
      authApi.redirectToLogin();
      return;
    }
  },

  /**
   * 输入框变化
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    this.validateForm();
  },

  /**
   * 分类选择变化
   */
  onCategoryChange(e) {
    const index = parseInt(e.detail.value);
    const category = this.data.categories[index];
    
    this.setData({
      categoryIndex: index,
      'formData.category': category.value
    });
  },

  /**
   * 截止时间选择
   */
  onDeadlineChange(e) {
    console.log('截止时间选择:', e.detail.value);
    this.setData({
      'formData.deadline': e.detail.value
    });
    this.validateForm();
  },

  /**
   * 开关变化
   */
  onSwitchChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 选择位置
   */
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功:', res);
        
        this.setData({
          'formData.latitude': res.latitude,
          'formData.longitude': res.longitude,
          'formData.address': res.address || res.name || '',
          'formData.locationName': res.name || res.address || '已选择位置',
          mapMarkers: [{
            id: 1,
            latitude: res.latitude,
            longitude: res.longitude,
            title: '任务位置'
          }]
        });
        
        this.validateForm();
      },
      fail: (error) => {
        console.error('选择位置失败:', error);
        if (error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要位置权限',
            content: '请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        }
      }
    });
  },

  /**
   * 获取当前位置
   */
  getCurrentLocation() {
    // 直接使用 chooseLocation 但传入当前位置作为默认位置
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取当前位置成功:', res);

        // 使用 chooseLocation 让用户在当前位置附近选择具体地址
        wx.chooseLocation({
          latitude: res.latitude,
          longitude: res.longitude,
          success: (locationRes) => {
            console.log('选择位置成功:', locationRes);

            this.setData({
              'formData.latitude': locationRes.latitude,
              'formData.longitude': locationRes.longitude,
              'formData.address': locationRes.address || locationRes.name || '',
              'formData.locationName': locationRes.name || '当前位置',
              mapMarkers: [{
                id: 1,
                latitude: locationRes.latitude,
                longitude: locationRes.longitude,
                title: '任务位置'
              }]
            });

            this.validateForm();

            wx.showToast({
              title: '位置设置成功',
              icon: 'success'
            });
          },
          fail: (error) => {
            console.error('选择位置失败:', error);
            // 如果用户取消选择，则使用坐标作为地址
            this.fallbackReverseGeocode(res.latitude, res.longitude);
          }
        });
      },
      fail: (error) => {
        console.error('获取当前位置失败:', error);
        wx.showModal({
          title: '获取位置失败',
          content: '请检查位置权限设置',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      }
    });
  },



  /**
   * 备用逆地理编码方案
   */
  fallbackReverseGeocode(latitude, longitude) {
    // 如果API调用失败，使用简化的地址格式
    const address = `纬度: ${latitude.toFixed(6)}, 经度: ${longitude.toFixed(6)}`;

    this.setData({
      'formData.latitude': latitude,
      'formData.longitude': longitude,
      'formData.address': address,
      'formData.locationName': '当前位置',
      mapMarkers: [{
        id: 1,
        latitude: latitude,
        longitude: longitude,
        title: '当前位置'
      }]
    });

    this.validateForm();

    wx.showToast({
      title: '已获取当前位置',
      icon: 'success'
    });

    // 提示用户手动输入详细地址
    wx.showModal({
      title: '提示',
      content: '已获取位置坐标，请手动输入详细地址',
      showCancel: false
    });
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { title, description, reward, address } = this.data.formData;
    
    const canPublish = !!(
      title.trim() &&
      description.trim() &&
      reward &&
      parseFloat(reward) > 0 &&
      address.trim()
    );
    
    this.setData({
      canPublish: canPublish
    });
  },

  /**
   * 预览任务
   */
  previewTask() {
    if (!this.data.canPublish) {
      wx.showToast({
        title: '请完善任务信息',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      showPreview: true
    });
  },

  /**
   * 关闭预览
   */
  closePreview() {
    this.setData({
      showPreview: false
    });
  },

  /**
   * 发布任务
   */
  async publishTask() {
    if (!this.data.canPublish) {
      wx.showToast({
        title: '请完善任务信息',
        icon: 'none'
      });
      return;
    }
    
    await this.submitTask();
  },

  /**
   * 确认发布（从预览弹窗）
   */
  async confirmPublish() {
    this.setData({
      showPreview: false
    });
    
    await this.submitTask();
  },

  /**
   * 提交任务
   */
  async submitTask() {
    if (this.data.isLoading) return;
    
    this.setData({
      isLoading: true
    });
    
    try {
      const taskData = {
        title: this.data.formData.title.trim(),
        description: this.data.formData.description.trim(),
        category: this.data.formData.category,
        reward: parseFloat(this.data.formData.reward),
        estimated_hours: this.data.formData.estimated_hours ? parseFloat(this.data.formData.estimated_hours) : null,
        deadline: this.data.formData.deadline || null,
        address: this.data.formData.address.trim(),
        latitude: this.data.formData.latitude,
        longitude: this.data.formData.longitude,
        auto_accept: this.data.formData.auto_accept
      };
      
      console.log('提交任务数据:', taskData);
      
      const response = await api.post('/tasks', taskData, {
        showLoading: true,
        loadingText: '发布中...'
      });
      
      console.log('任务发布成功:', response);
      
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('发布任务失败:', error);
      wx.showToast({
        title: error.message || '发布失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  }
});
